<?php

namespace App\Http\Controllers\Backend;

use App\Base\Controller\BaseController;
use App\Exports\OfflineCourseDebtExport;
use App\Exports\OfflineDebtExport;
use App\Exports\VipInvoiceExport;
use App\Http\Controllers\Controller;
use App\Http\Models\Invoice;
use App\Http\Models\School\Course;
use App\Http\Models\School\CourseTime;
use App\Http\Models\School\CourseTimeStudent;
use App\Http\Models\School\DayTime;
use App\Http\Models\School\Department;
use App\Http\Models\School\Room;
use App\Http\Models\School\SchoolUser;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Models\Vip\VipCombo;

class OfflineController extends BaseController
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\Response|\Illuminate\View\View
     */
    public function index()
    {
        return view('backend.offline.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function departments()
    {
        $data = Department::query()->whereIn('id', [1, 2, 19, 23])->get();
        return $this->statusOK($data);
    }

    public function times()
    {
        $data = DayTime::all();
        return $this->statusOK($data);
    }


    public function rooms()
    {
        $data = Room::orderBy('room_name')->get();
        return $this->statusOK($data);
    }

    public function teachers(Request $request)
    {
        $query = SchoolUser::query()->offlineTeacher();
        if (isset($request->department_id) && $request->department_id) {
            $query = $query->where('department_id', $request->department_id);
        }
        $data = $query->orderBy('first_name')->get();
        return $this->statusOK($data);
    }

    public function courses(Request $request)
    {
        $params = $request->all();
        $query = Course::query()
            ->select('id', 'building', 'deleted', 'level_of_n', 'total', 'date_start', 'date_end')
            ->with(['info.room' => function ($q) {
                $q->select('id', 'room_name', 'room_code', 'deleted', 'department_id');
            },
                'info.periods' => function ($q) {
                    $q->select('int', 'period_code', 'period_detail', 'value', 'deleted');
                }
            ])
            ->with(['students' => function ($q) {
                $q->select('users_tbl.id', 'first_name', 'last_name', 'dmr_id')->with(['user_dmr' => function ($q) {
                    $q->select('id', 'name', 'email');
                }, 'invoice' => function ($q) {
                    $q->select('id', 'price', 'user_id', 'payment_status', 'invoice_status', 'discount_money', 'paid_money', 'paid_for_id', 'note', 'created_at')
                        ->orderBy('created_at', 'desc')
                        ->where('paid_for_id', null)
                        ->where('product_type', 'offline')
                        ->where('invoice_status', '<>', 'canceled')
                        ->with(['children' => function ($q) {

                            $q->orderBy('created_at', 'desc')
                                ->where('product_type', 'offline')
                                ->where('invoice_status', '<>', 'canceled')
                                ->select('id', 'price', 'user_id', 'payment_status', 'invoice_status', 'discount_money', 'paid_money', 'paid_for_id', 'created_at');
                        }]);
                }]);
            }])
            ->with(['teachers' => function ($q) {
                $q->select('users_tbl.id', 'first_name', 'last_name')->wherePivot('deleted', 0);
            }])
            ->with(['course_times' => function ($q) {
                $q->select('id', 'course_id', 'deleted')->where('deleted', 0);
            }]);
        if (isset($params['department']) && $params['department']) {
            $query = $query->where('building', $params['department']);
        }
        if (isset($params['level']) && $params['level']) {
            $query = $query->where('level_of_n', $params['level']);
        }
        if (isset($params['day_time']) && $params['day_time']) {
            $query = $query->whereHas('info', function ($q) use ($params) {
                $q->where('period_id', $params['day_time']);
            });
        }
        if (isset($params['room']) && $params['room']) {
            $query = $query->whereHas('info', function ($q) use ($params) {
                $q->where('room_id', $params['room']);
            });
        }
        if (isset($params['teacher']) && $params['teacher']) {
            $query = $query->whereHas('course_teacher', function ($q) use ($params) {
                $q->where('teacher_id', $params['teacher']);
            });
        }
        if (isset($params['level']) && $params['level']) {
            $query = $query->where('level_of_n', $params['level']);
        }

        if (isset($params['date_start']) && $params['date_start']) {
            $arr = explode(',', $params['date_start']);
            $query = $query->whereBetween('date_start', $arr);
        }

        if (isset($params['date_end']) && $params['date_end']) {
            $arr = explode(',', $params['date_end']);
            $query = $query->whereBetween('date_end', $arr);
        }

//      $query = $query->has('course_teacher');
        $data = $query->where('deleted', 0)
            ->where('date_start', '>', '2022-01-01')
            ->orderBy('date_start', 'desc')
            ->paginate(10);
        $res = [
            'perPage' => $data->perPage(),
            'currentPage' => $data->currentPage(),
            'total' => $data->total(),
            'lastPage' => $data->lastPage()
        ];
        $data = $data->map(function ($course) {
            $course->students = $course->students->map(function ($item) {
                if ($item->user_dmr != null && !$item->invoice->isEmpty()) {
                    foreach ($item->invoice as $invoice) {
                        if (Carbon::parse($invoice->created_at) < Carbon::parse('2024-07-01')) {
                            if ($invoice->children->isEmpty()) {
                                if ($invoice->price - $invoice->paid_money - $invoice->discount_money <= 0) {
                                    break;
                                } else {
                                    $item->is_debt = 1;
                                    $item->debt_amount = $invoice->price - $invoice->paid_money - $invoice->discount_money;
                                }
                            } else {
                                $invoice_last = $invoice->children->first();
                                if (($invoice_last->price - $invoice_last->paid_money - $invoice_last->discount_money > 0 || $invoice_last->payment_status != 'paid') && $invoice_last->invoice_status != 'canceled') {
                                    $item->is_debt = 1;
                                    $item->debt_amount = $invoice_last->price - $invoice_last->paid_money - $invoice_last->discount_money;
                                }
                            }

                        } else {
                            if ($invoice->children->isEmpty()) {
                                if (($invoice->price - $invoice->paid_money - $invoice->discount_money > 0 || $invoice->payment_status != 'paid') && $invoice->invoice_status != 'canceled') {
                                    $item->is_debt = 1;
                                    $item->debt_amount = $invoice->price - $invoice->paid_money - $invoice->discount_money;
                                }
                            } else {
                                $invoice_last = $invoice->children->first();
                                if (($invoice_last->price - $invoice_last->paid_money - $invoice_last->discount_money > 0 || $invoice_last->payment_status != 'paid') && $invoice_last->invoice_status != 'canceled') {
                                    $item->is_debt = 1;
                                    $item->debt_amount = $invoice_last->price - $invoice_last->paid_money - $invoice_last->discount_money;
                                }
                            }
                        }
                    }
                }
                return $item;
            });
            return $course;
        });
        $res['data'] = $data;
        $courseTimeStudent = CourseTimeStudent::query()
            ->select('course_time_id as ctd', 'student_id as sid', 'status as stt', 'deleted as dlt')
            ->whereIntegerInRaw('student_id', array_merge(...$data->pluck('students.*.id')))
            ->whereIntegerInRaw('course_time_id', array_merge(...$data->pluck('course_times.*.id')))
            ->where('deleted', 0)
            ->where('status', 1)
            ->toBase()
            ->get();
        return $this->statusOK([
            'course_time' => $courseTimeStudent,
            'data' => $res
        ]);
    }

    public function coursesOld(Request $request)
    {
        $params = $request->all();
        $query = Course::query()
            ->select('id', 'building', 'deleted', 'level_of_n', 'total', 'date_start', 'date_end')
            ->with(['info.room' => function ($q) {
                $q->select('id', 'room_name', 'room_code', 'deleted', 'department_id');
            },
                'info.periods' => function ($q) {
                    $q->select('int', 'period_code', 'period_detail', 'value', 'deleted');
                }
            ])
            ->with(['students' => function ($q) {
                $q->select('users_tbl.id', 'first_name', 'last_name');
            }])
            ->with(['teachers' => function ($q) {
                $q->select('users_tbl.id', 'first_name', 'last_name')->wherePivot('deleted', 0);
            }])
            ->with(['course_times' => function ($q) {
                $q->select('id', 'course_id', 'deleted')->where('deleted', 0);
            }]);
        if (isset($params['department']) && $params['department']) {
            $query = $query->where('building', $params['department']);
        }
        if (isset($params['level']) && $params['level']) {
            $query = $query->where('level_of_n', $params['level']);
        }
        if (isset($params['day_time']) && $params['day_time']) {
            $query = $query->whereHas('info', function ($q) use ($params) {
                $q->where('period_id', $params['day_time']);
            });
        }
        if (isset($params['room']) && $params['room']) {
            $query = $query->whereHas('info', function ($q) use ($params) {
                $q->where('room_id', $params['room']);
            });
        }
        if (isset($params['teacher']) && $params['teacher']) {
            $query = $query->whereHas('course_teacher', function ($q) use ($params) {
                $q->where('teacher_id', $params['teacher']);
            });
        }
        if (isset($params['level']) && $params['level']) {
            $query = $query->where('level_of_n', $params['level']);
        }

        if (isset($params['date_start']) && $params['date_start']) {
            $arr = explode(',', $params['date_start']);
            $query = $query->whereBetween('date_start', $arr);
        }

        if (isset($params['date_end']) && $params['date_end']) {
            $arr = explode(',', $params['date_end']);
            $query = $query->whereBetween('date_end', $arr);
        }

//      $query = $query->has('course_teacher');
        $data = $query->where('deleted', 0)
            ->where('date_start', '>', '2022-01-01')
            ->orderBy('date_start', 'desc')
            ->get();
        $courseTimeStudent = CourseTimeStudent::query()
            ->select('course_time_id as ctd', 'student_id as sid', 'status as stt', 'deleted as dlt')
            ->whereIntegerInRaw('student_id', array_merge(...$data->pluck('students.*.id')))
            ->whereIntegerInRaw('course_time_id', array_merge(...$data->pluck('course_times.*.id')))
            ->where('deleted', 0)
            ->where('status', 1)
            ->toBase()
            ->get();
        return $this->statusOK([
            'course_time' => $courseTimeStudent,
            'data' => $data
        ]);
    }

    public function courseTime($id)
    {
        $query = CourseTime::query()->with('present.student')->with('absence.student')->where('course_id', $id)->where('deleted', 0);

        $data = $query->orderBy('index')->get();
        return $this->statusOK($data);
    }

    public function debts()
    {
        $data = $this->getUnpaid();

        return $this->statusOK($data);
    }

    /**
     * dept số tiền nợ
     * price Giá trị đơn hàng
     * discount_money Tiền chiết khấu
     * paid_money Thực thu
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    private function getUnpaid()
    {
        // dept = price - discount_money - paid_money
        return Invoice::query()
            ->selectRaw('id, currency, department_id, discount_money, info_contact, invoice_status, 
            paid_money, payment_status, product_id, product_name, product_type, user_id, created_at,
            SUM(paid_money) as total_paid, MAX(price) as revenue, MAX(discount_money) as discount')
            ->with('department:id,name')
            ->with(['user' => function ($q) {
                $q->with(['offline_user' => function ($q) {
                    $q->select('id', 'first_name', 'last_name', 'email', 'phone', 'department_id', 'dmr_id')
                        ->with('courses:id,level_of_l,level_of_n,deleted')
                        ->with(['courses.info' => function ($q) {
                            $q->select('id', 'period_id', 'room_id', 'deleted', 'day_id', 'course_id')->with('room:id,department_id,deleted,room_name')->with('periods:int,deleted,period_code,value');
                        }])
                        ->with('courses.infos:id,period_id,room_id,deleted,day_id,course_id')
                        ->with('courses.teachers:id,first_name,last_name');
                }])->select('id', 'name', 'email', 'phone', 'phone_number');
            }])
            ->where('product_type', 'offline')
            ->where('invoice_status', '<>', 'canceled')
            ->havingRaw('MAX(price)-SUM(discount_money)-SUM(paid_money) > 0')
            ->orderBy('created_at', 'DESC')
            ->orderByRaw('MAX(price)-SUM(discount_money)-SUM(paid_money) DESC')
            ->groupBy('user_id')
            ->groupBy('product_id')
            ->get()->makeHidden(['logs', 'book_info']);
    }

    public function exportDebts()
    {
        $data = $this->getUnpaid()->toArray();
        return Excel::download(new OfflineDebtExport(
            $data
        ), 'invoices.xlsx');
    }

    public function exportCourseDebts($id)
    {
        $course = Course::find($id);
        $course = Course::query()->with(['students' => function ($q) {
                $q->select('users_tbl.id', 'first_name', 'last_name', 'dmr_id', 'email', 'phone', 'facebook_url')->with(['user_dmr' => function ($q) {
                    $q->select('id', 'name', 'email');
                }, 'invoice' => function ($q) {
                    $q->select('id', 'price', 'user_id', 'payment_status', 'invoice_status', 'discount_money', 'paid_money', 'paid_for_id', 'note', 'created_at')
                        ->orderBy('created_at', 'desc')
                        ->where('paid_for_id', null)
                        ->where('product_type', 'offline')
                        ->where('invoice_status', '<>', 'canceled')
                        ->with(['children' => function ($q) {

                            $q->orderBy('created_at', 'desc')
                                ->where('product_type', 'offline')
                                ->where('invoice_status', '<>', 'canceled')
                                ->select('id', 'price', 'user_id', 'payment_status', 'invoice_status', 'discount_money', 'paid_money', 'paid_for_id', 'created_at');
                        }]);
                }]);
            }])->find($id);
        $students = $course->students->map(function ($item) {
            if ($item->user_dmr != null && !$item->invoice->isEmpty()) {
                foreach ($item->invoice as $invoice) {
                    if (Carbon::parse($invoice->created_at) < Carbon::parse('2024-07-01')) {
                        if ($invoice->children->isEmpty()) {
                            if ($invoice->price - $invoice->paid_money - $invoice->discount_money <= 0) {
                                break;
                            } else {
                                $item->is_debt = 1;
                                $item->debt_amount = $invoice->price - $invoice->paid_money - $invoice->discount_money;
                            }
                        } else {
                            $invoice_last = $invoice->children->first();
                            if (($invoice_last->price - $invoice_last->paid_money - $invoice_last->discount_money > 0 || $invoice_last->payment_status != 'paid') && $invoice_last->invoice_status != 'canceled') {
                                $item->is_debt = 1;
                                $item->debt_amount = $invoice_last->price - $invoice_last->paid_money - $invoice_last->discount_money;
                            }
                        }

                    } else {
                        if ($invoice->children->isEmpty()) {
                            if (($invoice->price - $invoice->paid_money - $invoice->discount_money > 0 || $invoice->payment_status != 'paid') && $invoice->invoice_status != 'canceled') {
                                $item->is_debt = 1;
                                $item->debt_amount = $invoice->price - $invoice->paid_money - $invoice->discount_money;
                            }
                        } else {
                            $invoice_last = $invoice->children->first();
                            if (($invoice_last->price - $invoice_last->paid_money - $invoice_last->discount_money > 0 || $invoice_last->payment_status != 'paid') && $invoice_last->invoice_status != 'canceled') {
                                $item->is_debt = 1;
                                $item->debt_amount = $invoice_last->price - $invoice_last->paid_money - $invoice_last->discount_money;
                            }
                        }
                    }
                }
            }
            return $item;
        });
        return Excel::download(new OfflineCourseDebtExport(
            $students->toArray()
        ), $course->id . '-danh-sach-hoc-vien.xlsx');
    }
    public function getOfflineCombo()
    {
        $courses = VipCombo::where('type', 5)->get();
        return response()->json([
            'data' => $courses
        ]);
    }
}
