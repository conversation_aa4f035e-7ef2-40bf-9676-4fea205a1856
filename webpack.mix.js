let mix = require("laravel-mix");
const tailwindcss = require("tailwindcss"); /* Add this line at the top */

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for your application, as well as bundling up your JS files.
 |
 */
// mix.webpackConfig(webpack => {
//     return {
//         plugins: [
//             new webpack.NormalModuleReplacementPlugin(/element-ui[\/\\]lib[\/\\]locale[\/\\]lang[\/\\]zh-CN/, "element-ui/lib/locale/lang/en"),
//         ]
//     };
// });

mix
  .js("resources/assets/js/app.js", "public/js")
  .vue({ version: 2 })
  .sass("resources/assets/sass/app.scss", "public/css")
  .options({
    postCss: [tailwindcss("./tailwind.config.js")],
  })
  .version();

mix.js("resources/assets/js/canon.js", "public/js/canon").vue({ version: 2 })
    .sass("resources/assets/sass/canon/index.scss", "public/css/canon")
    .options({
        postCss: [ tailwindcss("./tailwind.config.js") ],
    })
    .version();

// mix.js("resources/assets/js/mkt.js", "public/js/mkt").vue({ version: 2 })
//     .sass("resources/assets/sass/mkt/index.scss", "public/css/mkt");
// mix.js("resources/assets/js/mkt.js", "public/js/mkt").vue({ version: 2 })
//     .sass("resources/assets/sass/mkt/index.scss", "public/css/mkt")
//     .options({
//         postCss: [ tailwindcss("./tailwind.config.js") ],
//     })
//     .version();

mix
  .js("resources/assets/js/video.js", "public/js/video")
  .vue({ version: 2 })
  .sass("resources/assets/sass/video/index.scss", "public/css/video")
  .options({
    postCss: [tailwindcss("./tailwind.config.js")],
  })
  .version();

mix.js("resources/assets/js/school.js", "public/js/school").vue({ version: 2 })
    .sass("resources/assets/sass/school/index.scss", "public/css/school")
    .options({
      postCss: [ tailwindcss("./tailwind.config.js") ],
    })
    .version();
// mix.js("resources/assets/js/teacher.js", "public/js/teacher").vue({ version: 2 })
//     .sass("resources/assets/sass/teacher/index.scss", "public/css/teacher")
//     .options({
//       postCss: [ tailwindcss("./tailwind.config.js") ],
//     })
//     .version();
// mix.sass("resources/assets/sass/index.scss", "public/css")
//     .options({
//         postCss: [ tailwindcss("./tailwind.config.js") ],
//     })
//     .version();

// Compile js cho popup thông báo header
mix
  .js("resources/assets/js/frontend/announce.js", "public/js")
  .vue({ version: 2 });

mix
  .js(
    [
      "resources/assets/js/backend/course-stage/stage-panel.js",
      "resources/assets/js/backend/course-stage/category-panel.js",
      "resources/assets/js/backend/course-stage/group-panel.js",
      "resources/assets/js/backend/course-stage/course-stage.js",
    ],
    "public/assets/backend/js/course-stage/course-stage.js"
  )
  .version();

mix
  .js(
    "resources/assets/js/backend/lesson-component.js",
    "public/assets/backend/js/lesson-component.js"
  )
  .version();

mix
  .js(
    "resources/assets/js/backend/manager-flashcard.js",
    "public/assets/backend/js/manager-flashcard.js"
  )
  .version();

mix.js(
  ["./resources/assets/js/backend/jlpt_detail.js"],
  "./public/assets/backend/js/jlpt_detail.js"
);

mix
  .js("resources/assets/js/course/basic.js", "public/assets/js/course/basic.js")
  .sass(
    "resources/assets/sass/frontend/course/basic.scss",
    "public/css/course/basic.css"
  )
  .version();

mix
    .js("resources/assets/js/module/flashcard.js", "public/assets/js/module/flashcard.js")
    .sass(
        "resources/assets/sass/module/flashcard.scss",
        "public/css/module/flashcard.css"
    )
    .version();

mix
  .js(
    "resources/assets/js/course/lesson-new.js",
    "public/assets/js/course/lesson-new.js"
  )
  .version();

mix
  .js(
    "resources/assets/js/frontend/lesson-checkpoint.js",
    "public/assets/js/lesson-checkpoint.js"
  )
  .version();

mix
  .js(
    "resources/assets/js/frontend/jlpt-history.js",
    "public/assets/js/jlpt-history.js"
  )
  .version();

mix
  .js(
    "resources/assets/js/backend/flashcard/flashcard.js",
    "public/assets/js/flashcard/flashcard.js"
  )
  .sass(
    "resources/assets/sass/backend/flashcard.scss",
    "public/css/backend/flashcard.css"
  )
  .version();

mix
    .js(
        "resources/assets/js/vocabulary/vocabulary.js",
        "public/assets/js/vocabulary/vocabulary.js"
    )
    .version();
