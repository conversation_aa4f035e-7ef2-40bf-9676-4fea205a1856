/******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	// The require scope
/******/ 	var __webpack_require__ = {};
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
/*!*************************************************!*\
  !*** ./resources/assets/js/module/flashcard.js ***!
  \*************************************************/
__webpack_require__.r(__webpack_exports__);
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var StackedCards = /*#__PURE__*/function () {
  function StackedCards() {
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    _classCallCheck(this, StackedCards);
    this.options = _objectSpread({
      stackedView: "Top",
      rotate: true,
      visibleItems: 3,
      margin: 10,
      useOverlays: true
    }, options);
    this.currentPosition = 0;
    this.isFirstTime = true;
    this.elTrans = 0;
    this.container = document.getElementById("stacked-cards-block");
    this.cardsContainer = this.container.querySelector(".stackedcards-container");
    this.cards = Array.from(this.cardsContainer.children);
    this.rightOverlay = this.container.querySelector(".stackedcards-overlay.right");
    this.leftOverlay = this.container.querySelector(".stackedcards-overlay.left");
    this.maxElements = this.cards.length;
    this.options.visibleItems = Math.min(this.options.visibleItems, this.maxElements);
    this.init();
  }
  return _createClass(StackedCards, [{
    key: "init",
    value: function init() {
      var _this = this;
      this.setupCards();
      this.cards.forEach(function (card) {
        console.log('card: ', card);
        // Remove existing click handlers
        card.removeEventListener('click', _this.handleCardClick);
      });
      this.addEventListeners();
      this.updateUI();
      setTimeout(function () {
        return _this.container.classList.remove("init");
      }, 150);
    }
  }, {
    key: "setupCards",
    value: function setupCards() {
      var _this2 = this;
      var _this$options = this.options,
        stackedView = _this$options.stackedView,
        visibleItems = _this$options.visibleItems,
        margin = _this$options.margin;
      var marginTotal = margin * (visibleItems - 1);
      this.cardsContainer.style.marginBottom = "".concat(marginTotal, "px");
      this.elTrans = stackedView === "Top" ? marginTotal : 0;
      this.cards.slice(visibleItems).forEach(function (card) {
        card.classList.add("stackedcards-".concat(stackedView.toLowerCase()), "stackedcards--animatable", "stackedcards-origin-".concat(stackedView.toLowerCase()));
        card.style.cssText = "\n        z-index: 0;\n        opacity: 0;\n        transform: scale(".concat(1 - visibleItems * 0.04, ") translateY(").concat(_this2.elTrans, "px);\n      ");
      });
      this.updateActiveCard();
      this.setupOverlays();
    }
  }, {
    key: "setupOverlays",
    value: function setupOverlays() {
      var _this3 = this;
      if (!this.options.useOverlays) {
        this.leftOverlay.classList.add("stackedcards-overlay-hidden");
        this.rightOverlay.classList.add("stackedcards-overlay-hidden");
        return;
      }
      [this.leftOverlay, this.rightOverlay].forEach(function (overlay) {
        overlay.style.transform = "translateY(".concat(_this3.elTrans, "px)");
      });
    }
  }, {
    key: "updateActiveCard",
    value: function updateActiveCard() {
      if (this.cards[this.currentPosition]) {
        this.cards[this.currentPosition].classList.add("stackedcards-active");
      }
    }
  }, {
    key: "addEventListeners",
    value: function addEventListeners() {
      var _this4 = this;
      // First remove any existing click handlers
      this.cards.forEach(function (card) {
        card.removeEventListener('click', _this4.handleCardClick);
      });

      // Store reference to click handler for proper removal
      this.handleCardClick = function (e) {
        console.log("e: ", e);
        var targetElement = e.target;
        var shouldFlip = true;
        while (targetElement && targetElement !== e.currentTarget) {
          if (targetElement.classList.contains("noFlip") || targetElement.classList.contains("card_audio") || targetElement.classList.contains("underline") || targetElement.tagName === "BUTTON" || targetElement.tagName === "A" || targetElement.tagName === "SVG" || targetElement.tagName === "path" || targetElement.closest('.card-footer') !== null || targetElement.closest('[class*="cursor-pointer"]') !== null) {
            shouldFlip = false;
            break;
          }
          targetElement = targetElement.parentElement;
        }
        console.log("shouldFlip: ", shouldFlip);
        if (shouldFlip) {
          var version = "development";
          // kiểm tra mặt truowcss khi lật nếu là mặt trước
          if (!e.currentTarget.querySelector(".card-inner").classList.contains("flip") && [39, 40].includes(course_id) && parseInt(authUser.isTester) === 0 && ["production", "prod"].includes(version)) {
            var event = parseInt(course_id) === 39 ? "n5_flc_behind" : "n4_flc_behind";

            // check function ga() co tai khong
            if (typeof ga !== 'undefined') {
              ga("send", "event", "nx_flc_behind", event, event);
            }
          }
          e.currentTarget.querySelector(".card-inner").classList.toggle("flip");
        }
      };

      // Add new click handlers
      this.cards.forEach(function (card) {
        card.addEventListener("click", _this4.handleCardClick);
      });
    }
  }, {
    key: "swipeLeft",
    value: function swipeLeft() {
      var _this5 = this;
      if (this.currentPosition >= this.maxElements) {
        return;
      }
      this.transformCard(-1000, 0, 0);
      if (this.options.useOverlays) {
        this.transformOverlay(this.leftOverlay, -1000, 0, 1, function () {
          return _this5.resetOverlay(_this5.leftOverlay);
        });
      }
      this.nextCard();
    }
  }, {
    key: "swipeRight",
    value: function swipeRight() {
      var _this6 = this;
      if (this.currentPosition >= this.maxElements) {
        return;
      }
      this.transformCard(1000, 0, 0);
      if (this.options.useOverlays) {
        this.transformOverlay(this.rightOverlay, 1000, 0, 1, function () {
          return _this6.resetOverlay(_this6.rightOverlay);
        });
      }
      this.nextCard();
    }
  }, {
    key: "undo",
    value: function undo() {
      if (this.currentPosition <= 0) {
        return;
      }
      this.currentPosition--;
      this.updateUI();
      this.updateActiveCard();
    }
  }, {
    key: "nextCard",
    value: function nextCard() {
      this.currentPosition++;
      this.updateUI();
      this.updateActiveCard();
    }
  }, {
    key: "transformCard",
    value: function transformCard(x, y, opacity) {
      var card = this.cards[this.currentPosition];
      if (!card) {
        return;
      }
      card.classList.remove("no-transition");
      card.style.zIndex = 6;
      this.transformElement(card, x, y, opacity);
    }
  }, {
    key: "transformOverlay",
    value: function transformOverlay(overlay, x, y, opacity, callback) {
      overlay.classList.remove("no-transition");
      overlay.style.zIndex = 8;
      this.transformElement(overlay, x, y, opacity);
      setTimeout(callback, 300);
    }
  }, {
    key: "resetOverlay",
    value: function resetOverlay(overlay) {
      var _this7 = this;
      overlay.classList.add("no-transition");
      requestAnimationFrame(function () {
        overlay.style.transform = "translateY(".concat(_this7.elTrans, "px)");
        overlay.style.opacity = 0;
      });
      this.isFirstTime = false;
    }
  }, {
    key: "transformElement",
    value: function transformElement(element, x, y, opacity) {
      var _this8 = this;
      var rotate = this.options.rotate ? Math.min(Math.max(x / 10, -15), 15) : 0;
      requestAnimationFrame(function () {
        element.style.transform = "translateX(".concat(x, "px) translateY(").concat(y + _this8.elTrans, "px) rotate(").concat(rotate, "deg)");
        element.style.opacity = opacity;
      });
    }
  }, {
    key: "updateUI",
    value: function updateUI() {
      var _this9 = this;
      requestAnimationFrame(function () {
        var _this9$options = _this9.options,
          visibleItems = _this9$options.visibleItems,
          margin = _this9$options.margin;
        var zIndex = 5;
        var scale;
        _this9.cards.slice(_this9.currentPosition, _this9.currentPosition + visibleItems).forEach(function (card, i) {
          scale = zIndex === 4 ? "scale(0.97)" : zIndex === 3 ? "scale(0.95)" : "";
          card.style.cssText = "\n          z-index: ".concat(zIndex--, ";\n          opacity: 1;\n          transform: ").concat(scale, " translateY(").concat(margin * i * (1 - visibleItems * 0.04), "px);\n        ");
          card.classList.add("stackedcards--animatable");
        });
      });
    }
  }, {
    key: "destroy",
    value: function destroy() {
      // Remove event listeners to prevent memory leaks
      this.cards.forEach(function (card) {
        var cardInner = card.querySelector(".card-inner");
        if (cardInner) {
          var newCard = card.cloneNode(true);
          card.parentNode.replaceChild(newCard, card);
        }
      });
    }
  }]);
}();
/* harmony default export */ __webpack_exports__["default"] = (StackedCards);
/******/ })()
;