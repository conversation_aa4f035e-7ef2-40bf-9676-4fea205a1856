/******/ (function() { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/axios/index.js":
/*!*************************************!*\
  !*** ./node_modules/axios/index.js ***!
  \*************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = __webpack_require__(/*! ./lib/axios */ "./node_modules/axios/lib/axios.js");

/***/ }),

/***/ "./node_modules/axios/lib/adapters/xhr.js":
/*!************************************************!*\
  !*** ./node_modules/axios/lib/adapters/xhr.js ***!
  \************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ./../utils */ "./node_modules/axios/lib/utils.js");
var settle = __webpack_require__(/*! ./../core/settle */ "./node_modules/axios/lib/core/settle.js");
var buildURL = __webpack_require__(/*! ./../helpers/buildURL */ "./node_modules/axios/lib/helpers/buildURL.js");
var parseHeaders = __webpack_require__(/*! ./../helpers/parseHeaders */ "./node_modules/axios/lib/helpers/parseHeaders.js");
var isURLSameOrigin = __webpack_require__(/*! ./../helpers/isURLSameOrigin */ "./node_modules/axios/lib/helpers/isURLSameOrigin.js");
var createError = __webpack_require__(/*! ../core/createError */ "./node_modules/axios/lib/core/createError.js");
var btoa = (typeof window !== 'undefined' && window.btoa && window.btoa.bind(window)) || __webpack_require__(/*! ./../helpers/btoa */ "./node_modules/axios/lib/helpers/btoa.js");

module.exports = function xhrAdapter(config) {
  return new Promise(function dispatchXhrRequest(resolve, reject) {
    var requestData = config.data;
    var requestHeaders = config.headers;

    if (utils.isFormData(requestData)) {
      delete requestHeaders['Content-Type']; // Let the browser set it
    }

    var request = new XMLHttpRequest();
    var loadEvent = 'onreadystatechange';
    var xDomain = false;

    // For IE 8/9 CORS support
    // Only supports POST and GET calls and doesn't returns the response headers.
    // DON'T do this for testing b/c XMLHttpRequest is mocked, not XDomainRequest.
    if ( true &&
        typeof window !== 'undefined' &&
        window.XDomainRequest && !('withCredentials' in request) &&
        !isURLSameOrigin(config.url)) {
      request = new window.XDomainRequest();
      loadEvent = 'onload';
      xDomain = true;
      request.onprogress = function handleProgress() {};
      request.ontimeout = function handleTimeout() {};
    }

    // HTTP basic authentication
    if (config.auth) {
      var username = config.auth.username || '';
      var password = config.auth.password || '';
      requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);
    }

    request.open(config.method.toUpperCase(), buildURL(config.url, config.params, config.paramsSerializer), true);

    // Set the request timeout in MS
    request.timeout = config.timeout;

    // Listen for ready state
    request[loadEvent] = function handleLoad() {
      if (!request || (request.readyState !== 4 && !xDomain)) {
        return;
      }

      // The request errored out and we didn't get a response, this will be
      // handled by onerror instead
      // With one exception: request that using file: protocol, most browsers
      // will return status as 0 even though it's a successful request
      if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {
        return;
      }

      // Prepare the response
      var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;
      var responseData = !config.responseType || config.responseType === 'text' ? request.responseText : request.response;
      var response = {
        data: responseData,
        // IE sends 1223 instead of 204 (https://github.com/mzabriskie/axios/issues/201)
        status: request.status === 1223 ? 204 : request.status,
        statusText: request.status === 1223 ? 'No Content' : request.statusText,
        headers: responseHeaders,
        config: config,
        request: request
      };

      settle(resolve, reject, response);

      // Clean up request
      request = null;
    };

    // Handle low level network errors
    request.onerror = function handleError() {
      // Real errors are hidden from us by the browser
      // onerror should only fire if it's a network error
      reject(createError('Network Error', config, null, request));

      // Clean up request
      request = null;
    };

    // Handle timeout
    request.ontimeout = function handleTimeout() {
      reject(createError('timeout of ' + config.timeout + 'ms exceeded', config, 'ECONNABORTED',
        request));

      // Clean up request
      request = null;
    };

    // Add xsrf header
    // This is only done if running in a standard browser environment.
    // Specifically not if we're in a web worker, or react-native.
    if (utils.isStandardBrowserEnv()) {
      var cookies = __webpack_require__(/*! ./../helpers/cookies */ "./node_modules/axios/lib/helpers/cookies.js");

      // Add xsrf header
      var xsrfValue = (config.withCredentials || isURLSameOrigin(config.url)) && config.xsrfCookieName ?
          cookies.read(config.xsrfCookieName) :
          undefined;

      if (xsrfValue) {
        requestHeaders[config.xsrfHeaderName] = xsrfValue;
      }
    }

    // Add headers to the request
    if ('setRequestHeader' in request) {
      utils.forEach(requestHeaders, function setRequestHeader(val, key) {
        if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {
          // Remove Content-Type if data is undefined
          delete requestHeaders[key];
        } else {
          // Otherwise add header to the request
          request.setRequestHeader(key, val);
        }
      });
    }

    // Add withCredentials to request if needed
    if (config.withCredentials) {
      request.withCredentials = true;
    }

    // Add responseType to request if needed
    if (config.responseType) {
      try {
        request.responseType = config.responseType;
      } catch (e) {
        // Expected DOMException thrown by browsers not compatible XMLHttpRequest Level 2.
        // But, this can be suppressed for 'json' type as it can be parsed by default 'transformResponse' function.
        if (config.responseType !== 'json') {
          throw e;
        }
      }
    }

    // Handle progress if needed
    if (typeof config.onDownloadProgress === 'function') {
      request.addEventListener('progress', config.onDownloadProgress);
    }

    // Not all browsers support upload events
    if (typeof config.onUploadProgress === 'function' && request.upload) {
      request.upload.addEventListener('progress', config.onUploadProgress);
    }

    if (config.cancelToken) {
      // Handle cancellation
      config.cancelToken.promise.then(function onCanceled(cancel) {
        if (!request) {
          return;
        }

        request.abort();
        reject(cancel);
        // Clean up request
        request = null;
      });
    }

    if (requestData === undefined) {
      requestData = null;
    }

    // Send the request
    request.send(requestData);
  });
};


/***/ }),

/***/ "./node_modules/axios/lib/axios.js":
/*!*****************************************!*\
  !*** ./node_modules/axios/lib/axios.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ./utils */ "./node_modules/axios/lib/utils.js");
var bind = __webpack_require__(/*! ./helpers/bind */ "./node_modules/axios/lib/helpers/bind.js");
var Axios = __webpack_require__(/*! ./core/Axios */ "./node_modules/axios/lib/core/Axios.js");
var defaults = __webpack_require__(/*! ./defaults */ "./node_modules/axios/lib/defaults.js");

/**
 * Create an instance of Axios
 *
 * @param {Object} defaultConfig The default config for the instance
 * @return {Axios} A new instance of Axios
 */
function createInstance(defaultConfig) {
  var context = new Axios(defaultConfig);
  var instance = bind(Axios.prototype.request, context);

  // Copy axios.prototype to instance
  utils.extend(instance, Axios.prototype, context);

  // Copy context to instance
  utils.extend(instance, context);

  return instance;
}

// Create the default instance to be exported
var axios = createInstance(defaults);

// Expose Axios class to allow class inheritance
axios.Axios = Axios;

// Factory for creating new instances
axios.create = function create(instanceConfig) {
  return createInstance(utils.merge(defaults, instanceConfig));
};

// Expose Cancel & CancelToken
axios.Cancel = __webpack_require__(/*! ./cancel/Cancel */ "./node_modules/axios/lib/cancel/Cancel.js");
axios.CancelToken = __webpack_require__(/*! ./cancel/CancelToken */ "./node_modules/axios/lib/cancel/CancelToken.js");
axios.isCancel = __webpack_require__(/*! ./cancel/isCancel */ "./node_modules/axios/lib/cancel/isCancel.js");

// Expose all/spread
axios.all = function all(promises) {
  return Promise.all(promises);
};
axios.spread = __webpack_require__(/*! ./helpers/spread */ "./node_modules/axios/lib/helpers/spread.js");

module.exports = axios;

// Allow use of default import syntax in TypeScript
module.exports["default"] = axios;


/***/ }),

/***/ "./node_modules/axios/lib/cancel/Cancel.js":
/*!*************************************************!*\
  !*** ./node_modules/axios/lib/cancel/Cancel.js ***!
  \*************************************************/
/***/ (function(module) {

"use strict";


/**
 * A `Cancel` is an object that is thrown when an operation is canceled.
 *
 * @class
 * @param {string=} message The message.
 */
function Cancel(message) {
  this.message = message;
}

Cancel.prototype.toString = function toString() {
  return 'Cancel' + (this.message ? ': ' + this.message : '');
};

Cancel.prototype.__CANCEL__ = true;

module.exports = Cancel;


/***/ }),

/***/ "./node_modules/axios/lib/cancel/CancelToken.js":
/*!******************************************************!*\
  !*** ./node_modules/axios/lib/cancel/CancelToken.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var Cancel = __webpack_require__(/*! ./Cancel */ "./node_modules/axios/lib/cancel/Cancel.js");

/**
 * A `CancelToken` is an object that can be used to request cancellation of an operation.
 *
 * @class
 * @param {Function} executor The executor function.
 */
function CancelToken(executor) {
  if (typeof executor !== 'function') {
    throw new TypeError('executor must be a function.');
  }

  var resolvePromise;
  this.promise = new Promise(function promiseExecutor(resolve) {
    resolvePromise = resolve;
  });

  var token = this;
  executor(function cancel(message) {
    if (token.reason) {
      // Cancellation has already been requested
      return;
    }

    token.reason = new Cancel(message);
    resolvePromise(token.reason);
  });
}

/**
 * Throws a `Cancel` if cancellation has been requested.
 */
CancelToken.prototype.throwIfRequested = function throwIfRequested() {
  if (this.reason) {
    throw this.reason;
  }
};

/**
 * Returns an object that contains a new `CancelToken` and a function that, when called,
 * cancels the `CancelToken`.
 */
CancelToken.source = function source() {
  var cancel;
  var token = new CancelToken(function executor(c) {
    cancel = c;
  });
  return {
    token: token,
    cancel: cancel
  };
};

module.exports = CancelToken;


/***/ }),

/***/ "./node_modules/axios/lib/cancel/isCancel.js":
/*!***************************************************!*\
  !*** ./node_modules/axios/lib/cancel/isCancel.js ***!
  \***************************************************/
/***/ (function(module) {

"use strict";


module.exports = function isCancel(value) {
  return !!(value && value.__CANCEL__);
};


/***/ }),

/***/ "./node_modules/axios/lib/core/Axios.js":
/*!**********************************************!*\
  !*** ./node_modules/axios/lib/core/Axios.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var defaults = __webpack_require__(/*! ./../defaults */ "./node_modules/axios/lib/defaults.js");
var utils = __webpack_require__(/*! ./../utils */ "./node_modules/axios/lib/utils.js");
var InterceptorManager = __webpack_require__(/*! ./InterceptorManager */ "./node_modules/axios/lib/core/InterceptorManager.js");
var dispatchRequest = __webpack_require__(/*! ./dispatchRequest */ "./node_modules/axios/lib/core/dispatchRequest.js");
var isAbsoluteURL = __webpack_require__(/*! ./../helpers/isAbsoluteURL */ "./node_modules/axios/lib/helpers/isAbsoluteURL.js");
var combineURLs = __webpack_require__(/*! ./../helpers/combineURLs */ "./node_modules/axios/lib/helpers/combineURLs.js");

/**
 * Create a new instance of Axios
 *
 * @param {Object} instanceConfig The default config for the instance
 */
function Axios(instanceConfig) {
  this.defaults = instanceConfig;
  this.interceptors = {
    request: new InterceptorManager(),
    response: new InterceptorManager()
  };
}

/**
 * Dispatch a request
 *
 * @param {Object} config The config specific for this request (merged with this.defaults)
 */
Axios.prototype.request = function request(config) {
  /*eslint no-param-reassign:0*/
  // Allow for axios('example/url'[, config]) a la fetch API
  if (typeof config === 'string') {
    config = utils.merge({
      url: arguments[0]
    }, arguments[1]);
  }

  config = utils.merge(defaults, this.defaults, { method: 'get' }, config);
  config.method = config.method.toLowerCase();

  // Support baseURL config
  if (config.baseURL && !isAbsoluteURL(config.url)) {
    config.url = combineURLs(config.baseURL, config.url);
  }

  // Hook up interceptors middleware
  var chain = [dispatchRequest, undefined];
  var promise = Promise.resolve(config);

  this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
    chain.unshift(interceptor.fulfilled, interceptor.rejected);
  });

  this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
    chain.push(interceptor.fulfilled, interceptor.rejected);
  });

  while (chain.length) {
    promise = promise.then(chain.shift(), chain.shift());
  }

  return promise;
};

// Provide aliases for supported request methods
utils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {
  /*eslint func-names:0*/
  Axios.prototype[method] = function(url, config) {
    return this.request(utils.merge(config || {}, {
      method: method,
      url: url
    }));
  };
});

utils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {
  /*eslint func-names:0*/
  Axios.prototype[method] = function(url, data, config) {
    return this.request(utils.merge(config || {}, {
      method: method,
      url: url,
      data: data
    }));
  };
});

module.exports = Axios;


/***/ }),

/***/ "./node_modules/axios/lib/core/InterceptorManager.js":
/*!***********************************************************!*\
  !*** ./node_modules/axios/lib/core/InterceptorManager.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ./../utils */ "./node_modules/axios/lib/utils.js");

function InterceptorManager() {
  this.handlers = [];
}

/**
 * Add a new interceptor to the stack
 *
 * @param {Function} fulfilled The function to handle `then` for a `Promise`
 * @param {Function} rejected The function to handle `reject` for a `Promise`
 *
 * @return {Number} An ID used to remove interceptor later
 */
InterceptorManager.prototype.use = function use(fulfilled, rejected) {
  this.handlers.push({
    fulfilled: fulfilled,
    rejected: rejected
  });
  return this.handlers.length - 1;
};

/**
 * Remove an interceptor from the stack
 *
 * @param {Number} id The ID that was returned by `use`
 */
InterceptorManager.prototype.eject = function eject(id) {
  if (this.handlers[id]) {
    this.handlers[id] = null;
  }
};

/**
 * Iterate over all the registered interceptors
 *
 * This method is particularly useful for skipping over any
 * interceptors that may have become `null` calling `eject`.
 *
 * @param {Function} fn The function to call for each interceptor
 */
InterceptorManager.prototype.forEach = function forEach(fn) {
  utils.forEach(this.handlers, function forEachHandler(h) {
    if (h !== null) {
      fn(h);
    }
  });
};

module.exports = InterceptorManager;


/***/ }),

/***/ "./node_modules/axios/lib/core/createError.js":
/*!****************************************************!*\
  !*** ./node_modules/axios/lib/core/createError.js ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var enhanceError = __webpack_require__(/*! ./enhanceError */ "./node_modules/axios/lib/core/enhanceError.js");

/**
 * Create an Error with the specified message, config, error code, request and response.
 *
 * @param {string} message The error message.
 * @param {Object} config The config.
 * @param {string} [code] The error code (for example, 'ECONNABORTED').
 * @param {Object} [request] The request.
 * @param {Object} [response] The response.
 * @returns {Error} The created error.
 */
module.exports = function createError(message, config, code, request, response) {
  var error = new Error(message);
  return enhanceError(error, config, code, request, response);
};


/***/ }),

/***/ "./node_modules/axios/lib/core/dispatchRequest.js":
/*!********************************************************!*\
  !*** ./node_modules/axios/lib/core/dispatchRequest.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ./../utils */ "./node_modules/axios/lib/utils.js");
var transformData = __webpack_require__(/*! ./transformData */ "./node_modules/axios/lib/core/transformData.js");
var isCancel = __webpack_require__(/*! ../cancel/isCancel */ "./node_modules/axios/lib/cancel/isCancel.js");
var defaults = __webpack_require__(/*! ../defaults */ "./node_modules/axios/lib/defaults.js");

/**
 * Throws a `Cancel` if cancellation has been requested.
 */
function throwIfCancellationRequested(config) {
  if (config.cancelToken) {
    config.cancelToken.throwIfRequested();
  }
}

/**
 * Dispatch a request to the server using the configured adapter.
 *
 * @param {object} config The config that is to be used for the request
 * @returns {Promise} The Promise to be fulfilled
 */
module.exports = function dispatchRequest(config) {
  throwIfCancellationRequested(config);

  // Ensure headers exist
  config.headers = config.headers || {};

  // Transform request data
  config.data = transformData(
    config.data,
    config.headers,
    config.transformRequest
  );

  // Flatten headers
  config.headers = utils.merge(
    config.headers.common || {},
    config.headers[config.method] || {},
    config.headers || {}
  );

  utils.forEach(
    ['delete', 'get', 'head', 'post', 'put', 'patch', 'common'],
    function cleanHeaderConfig(method) {
      delete config.headers[method];
    }
  );

  var adapter = config.adapter || defaults.adapter;

  return adapter(config).then(function onAdapterResolution(response) {
    throwIfCancellationRequested(config);

    // Transform response data
    response.data = transformData(
      response.data,
      response.headers,
      config.transformResponse
    );

    return response;
  }, function onAdapterRejection(reason) {
    if (!isCancel(reason)) {
      throwIfCancellationRequested(config);

      // Transform response data
      if (reason && reason.response) {
        reason.response.data = transformData(
          reason.response.data,
          reason.response.headers,
          config.transformResponse
        );
      }
    }

    return Promise.reject(reason);
  });
};


/***/ }),

/***/ "./node_modules/axios/lib/core/enhanceError.js":
/*!*****************************************************!*\
  !*** ./node_modules/axios/lib/core/enhanceError.js ***!
  \*****************************************************/
/***/ (function(module) {

"use strict";


/**
 * Update an Error with the specified config, error code, and response.
 *
 * @param {Error} error The error to update.
 * @param {Object} config The config.
 * @param {string} [code] The error code (for example, 'ECONNABORTED').
 * @param {Object} [request] The request.
 * @param {Object} [response] The response.
 * @returns {Error} The error.
 */
module.exports = function enhanceError(error, config, code, request, response) {
  error.config = config;
  if (code) {
    error.code = code;
  }
  error.request = request;
  error.response = response;
  return error;
};


/***/ }),

/***/ "./node_modules/axios/lib/core/settle.js":
/*!***********************************************!*\
  !*** ./node_modules/axios/lib/core/settle.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var createError = __webpack_require__(/*! ./createError */ "./node_modules/axios/lib/core/createError.js");

/**
 * Resolve or reject a Promise based on response status.
 *
 * @param {Function} resolve A function that resolves the promise.
 * @param {Function} reject A function that rejects the promise.
 * @param {object} response The response.
 */
module.exports = function settle(resolve, reject, response) {
  var validateStatus = response.config.validateStatus;
  // Note: status is not exposed by XDomainRequest
  if (!response.status || !validateStatus || validateStatus(response.status)) {
    resolve(response);
  } else {
    reject(createError(
      'Request failed with status code ' + response.status,
      response.config,
      null,
      response.request,
      response
    ));
  }
};


/***/ }),

/***/ "./node_modules/axios/lib/core/transformData.js":
/*!******************************************************!*\
  !*** ./node_modules/axios/lib/core/transformData.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ./../utils */ "./node_modules/axios/lib/utils.js");

/**
 * Transform the data for a request or a response
 *
 * @param {Object|String} data The data to be transformed
 * @param {Array} headers The headers for the request or response
 * @param {Array|Function} fns A single function or Array of functions
 * @returns {*} The resulting transformed data
 */
module.exports = function transformData(data, headers, fns) {
  /*eslint no-param-reassign:0*/
  utils.forEach(fns, function transform(fn) {
    data = fn(data, headers);
  });

  return data;
};


/***/ }),

/***/ "./node_modules/axios/lib/defaults.js":
/*!********************************************!*\
  !*** ./node_modules/axios/lib/defaults.js ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
/* provided dependency */ var process = __webpack_require__(/*! process/browser.js */ "./node_modules/process/browser.js");


var utils = __webpack_require__(/*! ./utils */ "./node_modules/axios/lib/utils.js");
var normalizeHeaderName = __webpack_require__(/*! ./helpers/normalizeHeaderName */ "./node_modules/axios/lib/helpers/normalizeHeaderName.js");

var DEFAULT_CONTENT_TYPE = {
  'Content-Type': 'application/x-www-form-urlencoded'
};

function setContentTypeIfUnset(headers, value) {
  if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {
    headers['Content-Type'] = value;
  }
}

function getDefaultAdapter() {
  var adapter;
  if (typeof XMLHttpRequest !== 'undefined') {
    // For browsers use XHR adapter
    adapter = __webpack_require__(/*! ./adapters/xhr */ "./node_modules/axios/lib/adapters/xhr.js");
  } else if (typeof process !== 'undefined') {
    // For node use HTTP adapter
    adapter = __webpack_require__(/*! ./adapters/http */ "./node_modules/axios/lib/adapters/xhr.js");
  }
  return adapter;
}

var defaults = {
  adapter: getDefaultAdapter(),

  transformRequest: [function transformRequest(data, headers) {
    normalizeHeaderName(headers, 'Content-Type');
    if (utils.isFormData(data) ||
      utils.isArrayBuffer(data) ||
      utils.isBuffer(data) ||
      utils.isStream(data) ||
      utils.isFile(data) ||
      utils.isBlob(data)
    ) {
      return data;
    }
    if (utils.isArrayBufferView(data)) {
      return data.buffer;
    }
    if (utils.isURLSearchParams(data)) {
      setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');
      return data.toString();
    }
    if (utils.isObject(data)) {
      setContentTypeIfUnset(headers, 'application/json;charset=utf-8');
      return JSON.stringify(data);
    }
    return data;
  }],

  transformResponse: [function transformResponse(data) {
    /*eslint no-param-reassign:0*/
    if (typeof data === 'string') {
      try {
        data = JSON.parse(data);
      } catch (e) { /* Ignore */ }
    }
    return data;
  }],

  timeout: 0,

  xsrfCookieName: 'XSRF-TOKEN',
  xsrfHeaderName: 'X-XSRF-TOKEN',

  maxContentLength: -1,

  validateStatus: function validateStatus(status) {
    return status >= 200 && status < 300;
  }
};

defaults.headers = {
  common: {
    'Accept': 'application/json, text/plain, */*'
  }
};

utils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {
  defaults.headers[method] = {};
});

utils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {
  defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);
});

module.exports = defaults;


/***/ }),

/***/ "./node_modules/axios/lib/helpers/bind.js":
/*!************************************************!*\
  !*** ./node_modules/axios/lib/helpers/bind.js ***!
  \************************************************/
/***/ (function(module) {

"use strict";


module.exports = function bind(fn, thisArg) {
  return function wrap() {
    var args = new Array(arguments.length);
    for (var i = 0; i < args.length; i++) {
      args[i] = arguments[i];
    }
    return fn.apply(thisArg, args);
  };
};


/***/ }),

/***/ "./node_modules/axios/lib/helpers/btoa.js":
/*!************************************************!*\
  !*** ./node_modules/axios/lib/helpers/btoa.js ***!
  \************************************************/
/***/ (function(module) {

"use strict";


// btoa polyfill for IE<10 courtesy https://github.com/davidchambers/Base64.js

var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';

function E() {
  this.message = 'String contains an invalid character';
}
E.prototype = new Error;
E.prototype.code = 5;
E.prototype.name = 'InvalidCharacterError';

function btoa(input) {
  var str = String(input);
  var output = '';
  for (
    // initialize result and counter
    var block, charCode, idx = 0, map = chars;
    // if the next str index does not exist:
    //   change the mapping table to "="
    //   check if d has no fractional digits
    str.charAt(idx | 0) || (map = '=', idx % 1);
    // "8 - idx % 1 * 8" generates the sequence 2, 4, 6, 8
    output += map.charAt(63 & block >> 8 - idx % 1 * 8)
  ) {
    charCode = str.charCodeAt(idx += 3 / 4);
    if (charCode > 0xFF) {
      throw new E();
    }
    block = block << 8 | charCode;
  }
  return output;
}

module.exports = btoa;


/***/ }),

/***/ "./node_modules/axios/lib/helpers/buildURL.js":
/*!****************************************************!*\
  !*** ./node_modules/axios/lib/helpers/buildURL.js ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ./../utils */ "./node_modules/axios/lib/utils.js");

function encode(val) {
  return encodeURIComponent(val).
    replace(/%40/gi, '@').
    replace(/%3A/gi, ':').
    replace(/%24/g, '$').
    replace(/%2C/gi, ',').
    replace(/%20/g, '+').
    replace(/%5B/gi, '[').
    replace(/%5D/gi, ']');
}

/**
 * Build a URL by appending params to the end
 *
 * @param {string} url The base of the url (e.g., http://www.google.com)
 * @param {object} [params] The params to be appended
 * @returns {string} The formatted url
 */
module.exports = function buildURL(url, params, paramsSerializer) {
  /*eslint no-param-reassign:0*/
  if (!params) {
    return url;
  }

  var serializedParams;
  if (paramsSerializer) {
    serializedParams = paramsSerializer(params);
  } else if (utils.isURLSearchParams(params)) {
    serializedParams = params.toString();
  } else {
    var parts = [];

    utils.forEach(params, function serialize(val, key) {
      if (val === null || typeof val === 'undefined') {
        return;
      }

      if (utils.isArray(val)) {
        key = key + '[]';
      }

      if (!utils.isArray(val)) {
        val = [val];
      }

      utils.forEach(val, function parseValue(v) {
        if (utils.isDate(v)) {
          v = v.toISOString();
        } else if (utils.isObject(v)) {
          v = JSON.stringify(v);
        }
        parts.push(encode(key) + '=' + encode(v));
      });
    });

    serializedParams = parts.join('&');
  }

  if (serializedParams) {
    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;
  }

  return url;
};


/***/ }),

/***/ "./node_modules/axios/lib/helpers/combineURLs.js":
/*!*******************************************************!*\
  !*** ./node_modules/axios/lib/helpers/combineURLs.js ***!
  \*******************************************************/
/***/ (function(module) {

"use strict";


/**
 * Creates a new URL by combining the specified URLs
 *
 * @param {string} baseURL The base URL
 * @param {string} relativeURL The relative URL
 * @returns {string} The combined URL
 */
module.exports = function combineURLs(baseURL, relativeURL) {
  return relativeURL
    ? baseURL.replace(/\/+$/, '') + '/' + relativeURL.replace(/^\/+/, '')
    : baseURL;
};


/***/ }),

/***/ "./node_modules/axios/lib/helpers/cookies.js":
/*!***************************************************!*\
  !*** ./node_modules/axios/lib/helpers/cookies.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ./../utils */ "./node_modules/axios/lib/utils.js");

module.exports = (
  utils.isStandardBrowserEnv() ?

  // Standard browser envs support document.cookie
  (function standardBrowserEnv() {
    return {
      write: function write(name, value, expires, path, domain, secure) {
        var cookie = [];
        cookie.push(name + '=' + encodeURIComponent(value));

        if (utils.isNumber(expires)) {
          cookie.push('expires=' + new Date(expires).toGMTString());
        }

        if (utils.isString(path)) {
          cookie.push('path=' + path);
        }

        if (utils.isString(domain)) {
          cookie.push('domain=' + domain);
        }

        if (secure === true) {
          cookie.push('secure');
        }

        document.cookie = cookie.join('; ');
      },

      read: function read(name) {
        var match = document.cookie.match(new RegExp('(^|;\\s*)(' + name + ')=([^;]*)'));
        return (match ? decodeURIComponent(match[3]) : null);
      },

      remove: function remove(name) {
        this.write(name, '', Date.now() - 86400000);
      }
    };
  })() :

  // Non standard browser env (web workers, react-native) lack needed support.
  (function nonStandardBrowserEnv() {
    return {
      write: function write() {},
      read: function read() { return null; },
      remove: function remove() {}
    };
  })()
);


/***/ }),

/***/ "./node_modules/axios/lib/helpers/isAbsoluteURL.js":
/*!*********************************************************!*\
  !*** ./node_modules/axios/lib/helpers/isAbsoluteURL.js ***!
  \*********************************************************/
/***/ (function(module) {

"use strict";


/**
 * Determines whether the specified URL is absolute
 *
 * @param {string} url The URL to test
 * @returns {boolean} True if the specified URL is absolute, otherwise false
 */
module.exports = function isAbsoluteURL(url) {
  // A URL is considered absolute if it begins with "<scheme>://" or "//" (protocol-relative URL).
  // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed
  // by any combination of letters, digits, plus, period, or hyphen.
  return /^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(url);
};


/***/ }),

/***/ "./node_modules/axios/lib/helpers/isURLSameOrigin.js":
/*!***********************************************************!*\
  !*** ./node_modules/axios/lib/helpers/isURLSameOrigin.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ./../utils */ "./node_modules/axios/lib/utils.js");

module.exports = (
  utils.isStandardBrowserEnv() ?

  // Standard browser envs have full support of the APIs needed to test
  // whether the request URL is of the same origin as current location.
  (function standardBrowserEnv() {
    var msie = /(msie|trident)/i.test(navigator.userAgent);
    var urlParsingNode = document.createElement('a');
    var originURL;

    /**
    * Parse a URL to discover it's components
    *
    * @param {String} url The URL to be parsed
    * @returns {Object}
    */
    function resolveURL(url) {
      var href = url;

      if (msie) {
        // IE needs attribute set twice to normalize properties
        urlParsingNode.setAttribute('href', href);
        href = urlParsingNode.href;
      }

      urlParsingNode.setAttribute('href', href);

      // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils
      return {
        href: urlParsingNode.href,
        protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',
        host: urlParsingNode.host,
        search: urlParsingNode.search ? urlParsingNode.search.replace(/^\?/, '') : '',
        hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',
        hostname: urlParsingNode.hostname,
        port: urlParsingNode.port,
        pathname: (urlParsingNode.pathname.charAt(0) === '/') ?
                  urlParsingNode.pathname :
                  '/' + urlParsingNode.pathname
      };
    }

    originURL = resolveURL(window.location.href);

    /**
    * Determine if a URL shares the same origin as the current location
    *
    * @param {String} requestURL The URL to test
    * @returns {boolean} True if URL shares the same origin, otherwise false
    */
    return function isURLSameOrigin(requestURL) {
      var parsed = (utils.isString(requestURL)) ? resolveURL(requestURL) : requestURL;
      return (parsed.protocol === originURL.protocol &&
            parsed.host === originURL.host);
    };
  })() :

  // Non standard browser envs (web workers, react-native) lack needed support.
  (function nonStandardBrowserEnv() {
    return function isURLSameOrigin() {
      return true;
    };
  })()
);


/***/ }),

/***/ "./node_modules/axios/lib/helpers/normalizeHeaderName.js":
/*!***************************************************************!*\
  !*** ./node_modules/axios/lib/helpers/normalizeHeaderName.js ***!
  \***************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ../utils */ "./node_modules/axios/lib/utils.js");

module.exports = function normalizeHeaderName(headers, normalizedName) {
  utils.forEach(headers, function processHeader(value, name) {
    if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {
      headers[normalizedName] = value;
      delete headers[name];
    }
  });
};


/***/ }),

/***/ "./node_modules/axios/lib/helpers/parseHeaders.js":
/*!********************************************************!*\
  !*** ./node_modules/axios/lib/helpers/parseHeaders.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var utils = __webpack_require__(/*! ./../utils */ "./node_modules/axios/lib/utils.js");

/**
 * Parse headers into an object
 *
 * ```
 * Date: Wed, 27 Aug 2014 08:58:49 GMT
 * Content-Type: application/json
 * Connection: keep-alive
 * Transfer-Encoding: chunked
 * ```
 *
 * @param {String} headers Headers needing to be parsed
 * @returns {Object} Headers parsed into an object
 */
module.exports = function parseHeaders(headers) {
  var parsed = {};
  var key;
  var val;
  var i;

  if (!headers) { return parsed; }

  utils.forEach(headers.split('\n'), function parser(line) {
    i = line.indexOf(':');
    key = utils.trim(line.substr(0, i)).toLowerCase();
    val = utils.trim(line.substr(i + 1));

    if (key) {
      parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;
    }
  });

  return parsed;
};


/***/ }),

/***/ "./node_modules/axios/lib/helpers/spread.js":
/*!**************************************************!*\
  !*** ./node_modules/axios/lib/helpers/spread.js ***!
  \**************************************************/
/***/ (function(module) {

"use strict";


/**
 * Syntactic sugar for invoking a function and expanding an array for arguments.
 *
 * Common use case would be to use `Function.prototype.apply`.
 *
 *  ```js
 *  function f(x, y, z) {}
 *  var args = [1, 2, 3];
 *  f.apply(null, args);
 *  ```
 *
 * With `spread` this example can be re-written.
 *
 *  ```js
 *  spread(function(x, y, z) {})([1, 2, 3]);
 *  ```
 *
 * @param {Function} callback
 * @returns {Function}
 */
module.exports = function spread(callback) {
  return function wrap(arr) {
    return callback.apply(null, arr);
  };
};


/***/ }),

/***/ "./node_modules/axios/lib/utils.js":
/*!*****************************************!*\
  !*** ./node_modules/axios/lib/utils.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var bind = __webpack_require__(/*! ./helpers/bind */ "./node_modules/axios/lib/helpers/bind.js");
var isBuffer = __webpack_require__(/*! is-buffer */ "./node_modules/is-buffer/index.js");

/*global toString:true*/

// utils is a library of generic helper functions non-specific to axios

var toString = Object.prototype.toString;

/**
 * Determine if a value is an Array
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is an Array, otherwise false
 */
function isArray(val) {
  return toString.call(val) === '[object Array]';
}

/**
 * Determine if a value is an ArrayBuffer
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is an ArrayBuffer, otherwise false
 */
function isArrayBuffer(val) {
  return toString.call(val) === '[object ArrayBuffer]';
}

/**
 * Determine if a value is a FormData
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is an FormData, otherwise false
 */
function isFormData(val) {
  return (typeof FormData !== 'undefined') && (val instanceof FormData);
}

/**
 * Determine if a value is a view on an ArrayBuffer
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false
 */
function isArrayBufferView(val) {
  var result;
  if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {
    result = ArrayBuffer.isView(val);
  } else {
    result = (val) && (val.buffer) && (val.buffer instanceof ArrayBuffer);
  }
  return result;
}

/**
 * Determine if a value is a String
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is a String, otherwise false
 */
function isString(val) {
  return typeof val === 'string';
}

/**
 * Determine if a value is a Number
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is a Number, otherwise false
 */
function isNumber(val) {
  return typeof val === 'number';
}

/**
 * Determine if a value is undefined
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if the value is undefined, otherwise false
 */
function isUndefined(val) {
  return typeof val === 'undefined';
}

/**
 * Determine if a value is an Object
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is an Object, otherwise false
 */
function isObject(val) {
  return val !== null && typeof val === 'object';
}

/**
 * Determine if a value is a Date
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is a Date, otherwise false
 */
function isDate(val) {
  return toString.call(val) === '[object Date]';
}

/**
 * Determine if a value is a File
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is a File, otherwise false
 */
function isFile(val) {
  return toString.call(val) === '[object File]';
}

/**
 * Determine if a value is a Blob
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is a Blob, otherwise false
 */
function isBlob(val) {
  return toString.call(val) === '[object Blob]';
}

/**
 * Determine if a value is a Function
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is a Function, otherwise false
 */
function isFunction(val) {
  return toString.call(val) === '[object Function]';
}

/**
 * Determine if a value is a Stream
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is a Stream, otherwise false
 */
function isStream(val) {
  return isObject(val) && isFunction(val.pipe);
}

/**
 * Determine if a value is a URLSearchParams object
 *
 * @param {Object} val The value to test
 * @returns {boolean} True if value is a URLSearchParams object, otherwise false
 */
function isURLSearchParams(val) {
  return typeof URLSearchParams !== 'undefined' && val instanceof URLSearchParams;
}

/**
 * Trim excess whitespace off the beginning and end of a string
 *
 * @param {String} str The String to trim
 * @returns {String} The String freed of excess whitespace
 */
function trim(str) {
  return str.replace(/^\s*/, '').replace(/\s*$/, '');
}

/**
 * Determine if we're running in a standard browser environment
 *
 * This allows axios to run in a web worker, and react-native.
 * Both environments support XMLHttpRequest, but not fully standard globals.
 *
 * web workers:
 *  typeof window -> undefined
 *  typeof document -> undefined
 *
 * react-native:
 *  navigator.product -> 'ReactNative'
 */
function isStandardBrowserEnv() {
  if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {
    return false;
  }
  return (
    typeof window !== 'undefined' &&
    typeof document !== 'undefined'
  );
}

/**
 * Iterate over an Array or an Object invoking a function for each item.
 *
 * If `obj` is an Array callback will be called passing
 * the value, index, and complete array for each item.
 *
 * If 'obj' is an Object callback will be called passing
 * the value, key, and complete object for each property.
 *
 * @param {Object|Array} obj The object to iterate
 * @param {Function} fn The callback to invoke for each item
 */
function forEach(obj, fn) {
  // Don't bother if no value provided
  if (obj === null || typeof obj === 'undefined') {
    return;
  }

  // Force an array if not already something iterable
  if (typeof obj !== 'object' && !isArray(obj)) {
    /*eslint no-param-reassign:0*/
    obj = [obj];
  }

  if (isArray(obj)) {
    // Iterate over array values
    for (var i = 0, l = obj.length; i < l; i++) {
      fn.call(null, obj[i], i, obj);
    }
  } else {
    // Iterate over object keys
    for (var key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        fn.call(null, obj[key], key, obj);
      }
    }
  }
}

/**
 * Accepts varargs expecting each argument to be an object, then
 * immutably merges the properties of each object and returns result.
 *
 * When multiple objects contain the same key the later object in
 * the arguments list will take precedence.
 *
 * Example:
 *
 * ```js
 * var result = merge({foo: 123}, {foo: 456});
 * console.log(result.foo); // outputs 456
 * ```
 *
 * @param {Object} obj1 Object to merge
 * @returns {Object} Result of all merge properties
 */
function merge(/* obj1, obj2, obj3, ... */) {
  var result = {};
  function assignValue(val, key) {
    if (typeof result[key] === 'object' && typeof val === 'object') {
      result[key] = merge(result[key], val);
    } else {
      result[key] = val;
    }
  }

  for (var i = 0, l = arguments.length; i < l; i++) {
    forEach(arguments[i], assignValue);
  }
  return result;
}

/**
 * Extends object a by mutably adding to it the properties of object b.
 *
 * @param {Object} a The object to be extended
 * @param {Object} b The object to copy properties from
 * @param {Object} thisArg The object to bind function to
 * @return {Object} The resulting value of object a
 */
function extend(a, b, thisArg) {
  forEach(b, function assignValue(val, key) {
    if (thisArg && typeof val === 'function') {
      a[key] = bind(val, thisArg);
    } else {
      a[key] = val;
    }
  });
  return a;
}

module.exports = {
  isArray: isArray,
  isArrayBuffer: isArrayBuffer,
  isBuffer: isBuffer,
  isFormData: isFormData,
  isArrayBufferView: isArrayBufferView,
  isString: isString,
  isNumber: isNumber,
  isObject: isObject,
  isUndefined: isUndefined,
  isDate: isDate,
  isFile: isFile,
  isBlob: isBlob,
  isFunction: isFunction,
  isStream: isStream,
  isURLSearchParams: isURLSearchParams,
  isStandardBrowserEnv: isStandardBrowserEnv,
  forEach: forEach,
  merge: merge,
  extend: extend,
  trim: trim
};


/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=script&lang=js":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=script&lang=js ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  props: {
    percent: {
      type: [Number, String],
      "default": 0,
      validator: function validator(value) {
        var num = parseFloat(value);
        return !isNaN(num) && num >= 0 && num <= 100;
      }
    },
    widthPercent: {
      type: String,
      "default": "120px"
    },
    color: {
      type: String,
      "default": "#57D061"
    },
    height: {
      type: String,
      "default": "10px"
    },
    background: {
      type: String,
      "default": "white"
    }
  },
  data: function data() {
    return {
      width: "0%",
      height: "10px",
      background: "white",
      color: "#57D061",
      widthPercent: "120px"
    };
  },
  computed: {
    computedWidth: function computedWidth() {
      // Đảm bảo percent là số và thêm đơn vị %
      var percentValue = parseFloat(this.percent);
      return isNaN(percentValue) ? "0%" : "".concat(percentValue, "%");
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=script&lang=js":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=script&lang=js ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _module_flashcard__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../module/flashcard */ "./resources/assets/js/module/flashcard.js");

/* harmony default export */ __webpack_exports__["default"] = ({
  name: "FlashCardModule",
  props: {
    cards: {
      type: Array,
      required: true,
      "default": function _default() {
        return [];
      }
    },
    isJapanese: {
      type: Boolean,
      "default": true
    }
  },
  data: function data() {
    return {
      stackedCardsInstance: null,
      currentPosition: 0
    };
  },
  mounted: function mounted() {
    this.initStackedCards();
  },
  methods: {
    initStackedCards: function initStackedCards() {
      var _this = this;
      this.$nextTick(function () {
        _this.stackedCardsInstance = new _module_flashcard__WEBPACK_IMPORTED_MODULE_0__["default"]({
          visibleItems: 3,
          margin: 22,
          rotate: true,
          useOverlays: true
        });
        _this.updateCurrentFlashcard();
      });
    },
    updateCurrentFlashcard: function updateCurrentFlashcard() {
      if (!this.stackedCardsInstance) return;
      this.currentPosition = this.stackedCardsInstance.currentPosition;

      // Emit event with current card
      if (this.cards[this.currentPosition]) {
        this.$emit('card-changed', {
          card: this.cards[this.currentPosition],
          position: this.currentPosition
        });
      }

      // Check if we've reached the end
      if (this.currentPosition >= this.cards.length) {
        this.$emit('cards-completed');
      }
    },
    swipeLeft: function swipeLeft() {
      if (!this.stackedCardsInstance) return;
      var currentPos = this.stackedCardsInstance.currentPosition;
      var currentCard = this.cards[currentPos];
      if (currentCard) {
        this.$emit('card-swiped', {
          direction: 'left',
          card: currentCard,
          position: currentPos
        });
      }
      this.stackedCardsInstance.swipeLeft();
      this.updateCurrentFlashcard();
    },
    swipeRight: function swipeRight() {
      if (!this.stackedCardsInstance) return;
      var currentPos = this.stackedCardsInstance.currentPosition;
      var currentCard = this.cards[currentPos];
      if (currentCard) {
        this.$emit('card-swiped', {
          direction: 'right',
          card: currentCard,
          position: currentPos
        });
      }
      this.stackedCardsInstance.swipeRight();
      this.updateCurrentFlashcard();
    },
    playAudio: function playAudio(audioSrc) {
      if (!audioSrc) return;
      this.$emit('play-audio', audioSrc);

      // Fallback audio playing if parent doesn't handle it
      var audio = new Audio("https://video-test.dungmori.com/audios/".concat(audioSrc));
      audio.play();
    },
    flipCard: function flipCard() {
      this.$emit('flip-card');
    },
    resetCards: function resetCards() {
      if (this.stackedCardsInstance) {
        this.stackedCardsInstance.destroy();
        this.stackedCardsInstance = null;
      }
      this.initStackedCards();
    }
  },
  watch: {
    cards: {
      handler: function handler() {
        this.resetCards();
      },
      deep: true
    }
  },
  beforeDestroy: function beforeDestroy() {
    if (this.stackedCardsInstance) {
      this.stackedCardsInstance.destroy();
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/course/components/SearchBar.vue?vue&type=script&lang=js":
/*!*****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/course/components/SearchBar.vue?vue&type=script&lang=js ***!
  \*****************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({
  props: ["currentTab"],
  data: function data() {
    return {
      searchField: ""
    };
  },
  watch: {
    searchField: function searchField(value) {
      this.$emit("update-search", value);
    }
  },
  methods: {
    hideMenu: function hideMenu() {
      this.$emit("hide-menu");
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=script&lang=js":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=script&lang=js ***!
  \******************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _course_components_SearchBar_vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../course/components/SearchBar.vue */ "./resources/assets/js/course/components/SearchBar.vue");
/* harmony import */ var _components_FlashCard_vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/FlashCard.vue */ "./resources/assets/js/vocabulary/components/FlashCard.vue");


var TYPES_VIEW = {
  OVERVIEW: 1,
  SEARCH: 2,
  SPECIALIZED: 3,
  FAVORITE: 4
};
var searchResults = [{
  id: 1,
  word: "もり",
  description: "Rừng, rừng rậm",
  specialized: "JLPT N5"
}, {
  id: 2,
  word: "はな",
  description: "Lá",
  specialized: "JLPT N4"
}, {
  id: 3,
  word: "きのう",
  description: "Hôm qua",
  specialized: "Chuyên ngành Thực phẩm"
}, {
  id: 4,
  word: "あした",
  description: "Ngày mai",
  specialized: "Chuyên ngành Xây dựng"
}, {
  id: 4,
  word: "もり",
  description: "Rừng, rừng rậm",
  specialized: "JLPT N5"
}, {
  id: 5,
  word: "はな",
  description: "Lá",
  specialized: "JLPT N4"
}, {
  id: 6,
  word: "きのう",
  description: "Hôm qua",
  specialized: "Chuyên ngành Thực phẩm"
}, {
  id: 7,
  word: "あした",
  description: "Ngày mai",
  specialized: "Chuyên ngành Xây dựng"
}, {
  id: 8,
  word: "もり",
  description: "Rừng, rừng rậm",
  specialized: "JLPT N5"
}, {
  id: 9,
  word: "はな",
  description: "Lá",
  specialized: "JLPT N4"
}, {
  id: 10,
  word: "きのう",
  description: "Hôm qua",
  specialized: "Chuyên ngành Thực phẩm"
}, {
  id: 11,
  word: "あした",
  description: "Ngày mai",
  specialized: "Chuyên ngành Xây dựng"
}];
var cards = [{
  "id": 103569,
  "lesson_id": 10846,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>わたし</p>",
    "word_stress": "<p>わ<span style=\"text-decoration:overline\">たしr</span></p>",
    "word_type": "Động từ",
    "meaning": "<p>3</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p><span style=\"color:#ef6d13\">わたし</span>はきょうしです。</p>",
      "audio": null
    }],
    "meaning_example": ["<p><span style=\"color:#ef6d13\">T&ocirc;i</span> l&agrave; gi&aacute;o vi&ecirc;n.</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 3,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-25T01:45:14.000000Z",
  "created_at": "2025-04-10T01:40:02.000000Z",
  "answers": []
}, {
  "id": 103579,
  "lesson_id": 10846,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>風邪を</p>",
    "word_stress": "<p><span style=\"text-decoration:overline\">風</span>邪を</p>",
    "word_type": "Động từ",
    "meaning": "<p>4</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p>風邪をひいて</p>",
      "audio": null
    }],
    "meaning_example": ["<p>T&ocirc;i đi chơi với bạn</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 6,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-25T01:45:29.000000Z",
  "created_at": "2025-04-14T01:35:44.000000Z",
  "answers": []
}, {
  "id": 103580,
  "lesson_id": 10846,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>なぐられている</p>",
    "word_stress": "<p>なぐ<span style=\"text-decoration:overline\">られて</span>いる</p>",
    "word_type": "Danh từ",
    "meaning": "<p>5</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p>ハンマーでなぐられている</p>",
      "audio": null
    }],
    "meaning_example": ["<p>Tết n&agrave;y t&ocirc;i mua hoa mai</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 7,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-25T01:45:40.000000Z",
  "created_at": "2025-04-14T01:36:43.000000Z",
  "answers": []
}, {
  "id": 103581,
  "lesson_id": 10846,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p><ruby>隣<rt>となり</rt></ruby>の<ruby>部<rt>へ</rt></ruby></p>",
    "word_stress": "<p><span style=\"text-decoration:overline\">とな</span>りの</p>",
    "word_type": "Tính từ đuôi な",
    "meaning": "<p>6</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [],
    "meaning_example": [],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 8,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-25T01:45:50.000000Z",
  "created_at": "2025-04-14T01:37:36.000000Z",
  "answers": []
}, {
  "id": 103582,
  "lesson_id": 10846,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>優しくて</p>",
    "word_stress": "<p><span style=\"text-decoration:overline\">優しく</span>て</p>",
    "word_type": "Động từ",
    "meaning": "<p>7</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p>ttet</p>",
      "audio": null
    }],
    "meaning_example": [],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 9,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-25T01:46:00.000000Z",
  "created_at": "2025-04-14T01:37:52.000000Z",
  "answers": []
}, {
  "id": 103583,
  "lesson_id": 10846,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>パーテ</p>",
    "word_stress": "<p><span style=\"text-decoration:overline\">パー</span>テ</p>",
    "word_type": "Tính từ đuôi い",
    "meaning": "<p>8</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [],
    "meaning_example": [],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 10,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-25T01:46:11.000000Z",
  "created_at": "2025-04-14T01:38:15.000000Z",
  "answers": []
}];
/* harmony default export */ __webpack_exports__["default"] = ({
  name: "vocabulary",
  components: {
    FlashCard: _components_FlashCard_vue__WEBPACK_IMPORTED_MODULE_1__["default"],
    SearchBar: _course_components_SearchBar_vue__WEBPACK_IMPORTED_MODULE_0__["default"]
  },
  props: {
    list_vocabulary: {
      type: Object,
      required: true
    }
  },
  watch: {
    searchFocused: function searchFocused(newValue, oldValue) {
      console.log("currentView: ", this.currentView);
      console.log("searchFocused: ", newValue);
      if (newValue) {
        this.currentView = TYPES_VIEW.SEARCH;
      }
      console.log("currentView: ", this.currentView);
    },
    searchQuery: function searchQuery(newValue, oldValue) {
      console.log("searchQuery: ", newValue);
      if (newValue) {
        this.currentView = TYPES_VIEW.SEARCH;
        this.searchResults = searchResults;
      } else {
        this.searchResults = [];
      }
      console.log("currentView: ", this.currentView);
    }
  },
  data: function data() {
    return {
      searchQuery: '',
      searchFocused: false,
      showFlashcardPopup: false,
      isFlashcardFlipped: false,
      currentFlashcardIndex: 0,
      favoriteFlashcards: [{
        id: 1,
        word: '前',
        reading: 'まえ',
        meaning: 'Phía trước',
        example: {
          japanese: '前に行きましょう。',
          vietnamese: 'Hãy đi về phía trước.'
        }
      }, {
        id: 2,
        word: '後',
        reading: 'あと',
        meaning: 'Phía sau',
        example: {
          japanese: '後ろを見てください。',
          vietnamese: 'Hãy nhìn phía sau.'
        }
      }, {
        id: 3,
        word: '上',
        reading: 'うえ',
        meaning: 'Bên trên',
        example: {
          japanese: '上を見てください。',
          vietnamese: 'Hãy nhìn lên trên.'
        }
      }, {
        id: 4,
        word: '下',
        reading: 'した',
        meaning: 'Bên dưới',
        example: {
          japanese: '下を見てください。',
          vietnamese: 'Hãy nhìn xuống dưới.'
        }
      }, {
        id: 5,
        word: '左',
        reading: 'ひだり',
        meaning: 'Bên trái',
        example: {
          japanese: '左に曲がってください。',
          vietnamese: 'Hãy rẽ trái.'
        }
      }],
      currentView: TYPES_VIEW.FAVORITE,
      typesView: TYPES_VIEW,
      chart: null,
      categories: [],
      searchResults: searchResults,
      showDialogInfo: false,
      cards: cards,
      card: {
        "id": 103569,
        "lesson_id": 10846,
        "exam_part_id": null,
        "skill": 1,
        "mondai_id": null,
        "type": 17,
        "type_ld": null,
        "server": null,
        "video_name": null,
        "video_title": null,
        "value": {
          "word": "<p>わたし</p>",
          "word_stress": "<p>わ<span style=\"text-decoration:overline\">たしr</span></p>",
          "word_type": "Động từ",
          "meaning": "<p>3</p>",
          "audio": null,
          "front_image": null,
          "back_image": null,
          "example": [{
            "example": "<p><span style=\"color:#ef6d13\">わたし</span>はきょうしです。</p>",
            "audio": null
          }],
          "meaning_example": ["<p><span style=\"color:#ef6d13\">T&ocirc;i</span> l&agrave; gi&aacute;o vi&ecirc;n.</p>"],
          "quiz_question": [],
          "kanji_meaning": null
        },
        "suggest": null,
        "explain": null,
        "explain_mp3": null,
        "value_data": null,
        "grade": "",
        "sort": 3,
        "show": 1,
        "is_quiz": 0,
        "video_full": 0,
        "updated_at": "2025-04-25T01:45:14.000000Z",
        "created_at": "2025-04-10T01:40:02.000000Z",
        "answers": [],
        "comment": {
          "id": 463617,
          "content": "hi",
          "user_id": 540309,
          "count_like": 0,
          "created_at": "2025-05-16 09:54:28",
          "pin": 0,
          "parent_id": 0,
          "table_id": 103569,
          "table_name": "flashcard",
          "user_info": {
            "email": "<EMAIL>",
            "name": "Dinhsuu",
            "avatar": "1711587724_0_76076.jpeg",
            "userId": 540309
          },
          "time_created": "16/05/2025 09:54",
          "replies": [{
            "id": 463618,
            "table_id": 103569,
            "table_name": "flashcard",
            "user_id": 540308,
            "admin_log": null,
            "kwadmin_id": null,
            "content": "122",
            "img": null,
            "audio": null,
            "rate": 0,
            "is_tester": 0,
            "parent_id": 463617,
            "count_like": 0,
            "ulikes": null,
            "readed": 0,
            "tag_data": null,
            "status": 0,
            "pin": 0,
            "is_correct": 0,
            "updated_at": "2025-05-16 09:54:44",
            "created_at": "2025-05-16 09:54:44",
            "user_info": {
              "email": "<EMAIL>",
              "name": "Thu Vũ",
              "avatar": "1711587724_0_76076.jpeg",
              "userId": 540308
            },
            "time_created": "16/05/2025 09:54",
            "replies": null,
            "table_info": {
              "id": "",
              "course_id": "",
              "name": "",
              "SEOurl": "",
              "course_url": ""
            }
          }],
          "table_info": {
            "id": "",
            "course_id": "",
            "name": "",
            "SEOurl": "",
            "course_url": ""
          },
          "comment_like": []
        }
      },
      courseSpecializedActive: null
    };
  },
  computed: {
    chartData: function chartData() {
      var data = {
        learned: 0,
        temporary: 0,
        memorized: 0,
        mastered: 0
      };

      // Cộng dồn dữ liệu từ các danh mục được chọn
      this.categories.forEach(function (category) {
        if (category.selected) {
          data.learned += category.data.learned;
          data.temporary += category.data.temporary;
          data.memorized += category.data.memorized;
          data.mastered += category.data.mastered;
        }
      });
      return data;
    },
    // Tính tổng số từ vựng từ các danh mục được chọn
    totalWords: function totalWords() {
      var total = 0;
      this.categories.forEach(function (category) {
        if (category.selected) {
          // Sử dụng word_count từ dữ liệu gốc hoặc tổng số từ đã học
          total += category.data.learned;
        }
      });
      return total;
    },
    // Lấy flashcard hiện tại
    currentFlashcard: function currentFlashcard() {
      return this.favoriteFlashcards[this.currentFlashcardIndex] || {};
    }
  },
  methods: {
    // Mở popup flashcard
    openFlashcardPopup: function openFlashcardPopup() {
      var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
      this.currentFlashcardIndex = index;
      this.isFlashcardFlipped = false;
      this.showFlashcardPopup = true;
      document.body.style.overflow = 'hidden'; // Ngăn cuộn trang khi popup mở
    },
    // Đóng popup flashcard
    closeFlashcardPopup: function closeFlashcardPopup() {
      this.showFlashcardPopup = false;
      document.body.style.overflow = ''; // Cho phép cuộn trang trở lại
    },
    // Lật flashcard
    flipCard: function flipCard(id, event) {
      console.log("id: ", id);
      // Kiểm tra xem phần tử được click hoặc cha của nó có class noFlip, card_audio, hoặc là thẻ button không
      var isNonFlippable = event.target.closest('.noFlip, .card_audio, button');
      if (isNonFlippable) {
        console.log('Click on non-flippable element, skipping flip');
        return; // Không lật thẻ nếu click vào phần tử không cho phép
      }
      var cardElement = document.querySelector("#card-inner-".concat(id));
      if (cardElement) {
        cardElement.classList.toggle('flip');
      }
    },
    // Chuyển đến flashcard trước
    prevFlashcard: function prevFlashcard() {
      if (this.currentFlashcardIndex > 0) {
        this.currentFlashcardIndex--;
        this.isFlashcardFlipped = false;
      }
    },
    // Chuyển đến flashcard tiếp theo
    nextFlashcard: function nextFlashcard() {
      if (this.currentFlashcardIndex < this.favoriteFlashcards.length - 1) {
        this.currentFlashcardIndex++;
        this.isFlashcardFlipped = false;
      }
    },
    // Tính toán chiều cao cho các cột
    getBarHeight: function getBarHeight(value) {
      if (!value) return '0%';

      // Tìm giá trị lớn nhất trong dữ liệu
      var maxValue = Math.max(this.chartData.learned || 0, this.chartData.temporary || 0, this.chartData.memorized || 0, this.chartData.mastered || 0);

      // Nếu không có giá trị nào lớn hơn 0, trả về chiều cao tối thiểu
      if (maxValue <= 0) return '5%';

      // Tính phần trăm so với giá trị lớn nhất (tối đa 80% chiều cao container)
      var percentage = value / maxValue * 80;

      // Đảm bảo các cột có giá trị nhỏ vẫn có chiều cao tối thiểu
      return "".concat(Math.max(percentage, 5), "%");
    },
    // Thêm phương thức initCategories
    initCategories: function initCategories() {
      var _this = this;
      // Xóa danh sách categories hiện tại
      this.categories = [];

      // Thêm các danh mục JLPT
      if (this.list_vocabulary.jlpt && this.list_vocabulary.jlpt.data) {
        this.list_vocabulary.jlpt.data.forEach(function (item) {
          // Tạo dữ liệu mẫu cho biểu đồ
          var totalWords = item.word_count || 0;
          var data = {
            learned: Math.floor(totalWords * 0.7),
            // 70% đã học
            temporary: Math.floor(totalWords * 0.4),
            // 40% tạm nhớ
            memorized: Math.floor(totalWords * 0.2),
            // 20% ghi nhớ
            mastered: Math.floor(totalWords * 0.1) // 10% thuộc lòng
          };
          _this.categories.push({
            name: item.name,
            id: item.id,
            selected: item.name.includes('N5'),
            // Mặc định chọn N5
            data: data
          });
        });
      }

      // Thêm các danh mục chuyên ngành
      if (this.list_vocabulary.specialized && this.list_vocabulary.specialized.data) {
        this.list_vocabulary.specialized.data.forEach(function (item) {
          // Tạo dữ liệu mẫu cho biểu đồ (trong thực tế, bạn sẽ lấy dữ liệu từ API)
          var totalWords = item.word_count || 0;
          var data = {
            learned: Math.floor(totalWords * 0.6),
            // 60% đã học
            temporary: Math.floor(totalWords * 0.3),
            // 30% tạm nhớ
            memorized: Math.floor(totalWords * 0.15),
            // 15% ghi nhớ
            mastered: Math.floor(totalWords * 0.05) // 5% thuộc lòng
          };
          _this.categories.push({
            name: item.name,
            id: item.id,
            selected: false,
            data: data
          });
        });
      }
    },
    playAudio: function playAudio() {
      var id = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
      var audioUrl = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
      console.log("Play audio for id: ".concat(id, ", url: ").concat(audioUrl));
      console.log("this.$root: ", this.$root);
      if (this.$root.toggleMp3) {
        this.$root.toggleMp3(id, 'flashcard', "https://video-test.dungmori.com/audio/".concat(audioUrl));
      } else {
        console.error('toggleMp3 method not found in root instance');
      }
    },
    openCourseFlashCardDetail: function openCourseFlashCardDetail(courseId) {
      console.log("openCourseFlashCardDetail: ", courseId);
      this.courseSpecializedActive = courseId;
      this.currentView = this.typesView.SPECIALIZED;
    }
  },
  mounted: function mounted() {
    // Không cần khởi tạo gì đặc biệt vì biểu đồ HTML/CSS tự động render
    console.log('Component mounted, chart data:', this.chartData);
  },
  created: function created() {
    console.log("list_vocabulary", this.list_vocabulary);

    // Khởi tạo danh sách categories từ list_vocabulary
    this.initCategories();
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=script&lang=js":
/*!*********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=script&lang=js ***!
  \*********************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _component_ProgressBar_vue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../component/ProgressBar.vue */ "./resources/assets/js/component/ProgressBar.vue");
/* harmony import */ var _components_FlashCardModule_vue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/FlashCardModule.vue */ "./resources/assets/js/components/FlashCardModule.vue");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ "./node_modules/axios/index.js");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(axios__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _module_flashcard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../module/flashcard */ "./resources/assets/js/module/flashcard.js");
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }




var TYPES_VIEW = {
  OVERVIEW: 1,
  SEARCH: 2,
  STACKED_CARD: 3,
  RESULT: 4
};
var searchResults = [{
  id: 1,
  word: "もり",
  description: "Rừng, rừng rậm",
  specialized: "JLPT N5"
}, {
  id: 2,
  word: "はな",
  description: "Lá",
  specialized: "JLPT N4"
}, {
  id: 3,
  word: "きのう",
  description: "Hôm qua",
  specialized: "Chuyên ngành Thực phẩm",
  lock: true
}, {
  id: 4,
  word: "あした",
  description: "Ngày mai",
  specialized: "Chuyên ngành Xây dựng",
  lock: true
}, {
  id: 5,
  word: "もり",
  description: "Rừng, rừng rậm",
  specialized: "JLPT N5",
  lock: true
}, {
  id: 6,
  word: "はな",
  description: "Lá",
  specialized: "JLPT N4",
  lock: true
}, {
  id: 7,
  word: "きのう",
  description: "Hôm qua",
  specialized: "Chuyên ngành Thực phẩm",
  lock: true
}, {
  id: 8,
  word: "あした",
  description: "Ngày mai",
  specialized: "Chuyên ngành Xây dựng",
  lock: true
}, {
  id: 9,
  word: "もり",
  description: "Rừng, rừng rậm",
  specialized: "JLPT N5",
  lock: true
}, {
  id: 10,
  word: "はな",
  description: "Lá",
  specialized: "JLPT N4",
  lock: true
}, {
  id: 11,
  word: "きのう",
  description: "Hôm qua",
  specialized: "Chuyên ngành Thực phẩm",
  lock: true
}, {
  id: 12,
  word: "あした",
  description: "Ngày mai",
  specialized: "Chuyên ngành Xây dựng",
  lock: true
}];
var dataFlashcard = [{
  "id": 103570,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p><span style=\"color:#ff9900\"><ruby>日本</ruby></span></p><p><ruby><rt><span style=\"color:#ff9900\">にほん</span></rt></ruby><span style=\"color:#ff9900\">の&nbsp;</span></p>",
    "word_stress": "<p><span style=\"text-decoration:overline\">にほん</span>の</p>",
    "word_type": "Danh từ",
    "meaning": "<p>NHẬT</p><p>Tiếng Nhật của bạn c&oacute; tốt kh&ocirc;ng thế</p>",
    "audio": "2025-04-11/sample-3s.mp3",
    "front_image": "2025-04-11/WebStore-ld4_0.png",
    "back_image": null,
    "example": [{
      "example": "<p><span style=\"color:#ef6d13\"><span style=\"text-decoration:overline\">特定原</span></span>材料とくてい<span style=\"text-decoration:overline\">げん</span>ざいりょうに準<span style=\"text-decoration:overline\">じゅんず</span>るもの</p>",
      "audio": "2025-04-11/sample-6s.mp3"
    }, {
      "example": "<p><ruby>機器<rt>きき</rt></ruby>や<ruby>器具</ruby></p>",
      "audio": "2025-04-11/sample-3s.mp3"
    }],
    "meaning_example": ["<p>B&aacute;c của t&ocirc;i l&agrave; gi&aacute;o vi&ecirc;n dạy<span style=\"color:#ef6d13\"> tiếng Nhật&nbsp;</span>tại trung t&acirc;m Nhật ngữ Dũng Mori ieo EOKJ JNF kf lkp;wpe njjgbj; hhe kjijwi&nbsp;</p>", "<p>Học tiếng Nhật c&oacute; kh&oacute; kh&ocirc;ng?</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 1,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-05-13T01:14:59.000000Z",
  "created_at": "2025-04-11T07:57:41.000000Z",
  "answers": [],
  "comment": {
    "id": 463576,
    "content": "cmt 1",
    "user_id": 540422,
    "count_like": 3,
    "created_at": "2025-04-29 07:26:47",
    "pin": 0,
    "parent_id": 0,
    "table_id": 103570,
    "table_name": "flashcard",
    "user_info": {
      "email": "<EMAIL>",
      "name": "Quỳnh Anh",
      "avatar": "1711587724_0_76076.jpeg",
      "userId": 540422
    },
    "time_created": "29/04/2025 07:26",
    "replies": [{
      "id": 463577,
      "table_id": 103570,
      "table_name": "flashcard",
      "user_id": 540422,
      "admin_log": null,
      "kwadmin_id": null,
      "content": "124",
      "img": null,
      "audio": null,
      "rate": 0,
      "is_tester": 0,
      "parent_id": 463576,
      "count_like": 2,
      "ulikes": null,
      "readed": 0,
      "tag_data": null,
      "status": 0,
      "pin": 0,
      "is_correct": 0,
      "updated_at": "2025-04-29 07:27:03",
      "created_at": "2025-04-29 07:27:03",
      "user_info": {
        "email": "<EMAIL>",
        "name": "Quỳnh Anh",
        "avatar": "1711587724_0_76076.jpeg",
        "userId": 540422
      },
      "time_created": "29/04/2025 07:27",
      "replies": null,
      "table_info": {
        "id": "",
        "course_id": "",
        "name": "",
        "SEOurl": "",
        "course_url": ""
      }
    }],
    "table_info": {
      "id": "",
      "course_id": "",
      "name": "",
      "SEOurl": "",
      "course_url": ""
    },
    "comment_like": []
  }
}, {
  "id": 103571,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>はな</p>",
    "word_stress": "<p><span style=\"text-decoration:overline\">は</span>な</p>",
    "word_type": "Danh từ",
    "meaning": "<p>Hoa</p>",
    "audio": null,
    "front_image": null,
    "back_image": "2025-04-11/1712378501768.jpg",
    "example": [{
      "example": "<p>は他たの</p>",
      "audio": null
    }, {
      "example": "<p>なった疑うたがい</p>",
      "audio": null
    }],
    "meaning_example": ["<p>Đi ăn cơm</p>", "<p>Học b&agrave;i đến 10h tối.</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 2,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-11T07:59:29.000000Z",
  "created_at": "2025-04-11T07:59:29.000000Z",
  "answers": [],
  "comment": {
    "id": 463284,
    "content": "bdbbdb",
    "user_id": 540308,
    "count_like": 3,
    "created_at": "2025-04-15 15:29:28",
    "pin": 0,
    "parent_id": 0,
    "table_id": 103571,
    "table_name": "flashcard",
    "user_info": {
      "email": "<EMAIL>",
      "name": "Thu Vũ",
      "avatar": "1711587724_0_76076.jpeg",
      "userId": 540308
    },
    "time_created": "15/04/2025 15:29",
    "replies": [],
    "table_info": {
      "id": "",
      "course_id": "",
      "name": "",
      "SEOurl": "",
      "course_url": ""
    },
    "comment_like": [{
      "comment_id": 463284,
      "user_id": 540308
    }]
  }
}, {
  "id": 103572,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p><ruby>作業<rt>さぎょう</rt></ruby>ス</p>",
    "word_stress": "<p><span style=\"text-decoration:overline\">作業さ</span>ぎょうス</p>",
    "word_type": null,
    "meaning": "<p>Cuộc sống</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p>作業さぎ</p>",
      "audio": null
    }],
    "meaning_example": ["<p>Cuộc sống ở Nhật</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 3,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-11T08:00:45.000000Z",
  "created_at": "2025-04-11T08:00:45.000000Z",
  "answers": [],
  "comment": {
    "id": 463306,
    "content": "hello",
    "user_id": 540308,
    "count_like": 0,
    "created_at": "2025-04-19 15:34:10",
    "pin": 0,
    "parent_id": 0,
    "table_id": 103572,
    "table_name": "flashcard",
    "user_info": {
      "email": "<EMAIL>",
      "name": "Thu Vũ",
      "avatar": "1711587724_0_76076.jpeg",
      "userId": 540308
    },
    "time_created": "19/04/2025 15:34",
    "replies": [],
    "table_info": {
      "id": "",
      "course_id": "",
      "name": "",
      "SEOurl": "",
      "course_url": ""
    },
    "comment_like": []
  }
}, {
  "id": 103573,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>ならないよう</p>",
    "word_stress": "<p>なら<span style=\"text-decoration:overline\">ない</span>よう</p>",
    "word_type": "Động từ",
    "meaning": "<p>Th&uacute; vị</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [],
    "meaning_example": [],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 4,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-11T08:01:41.000000Z",
  "created_at": "2025-04-11T08:01:41.000000Z",
  "answers": [],
  "comment": {
    "id": 463604,
    "content": "fgfg",
    "user_id": 540308,
    "count_like": 0,
    "created_at": "2025-05-09 09:08:20",
    "pin": 0,
    "parent_id": 0,
    "table_id": 103573,
    "table_name": "flashcard",
    "user_info": {
      "email": "<EMAIL>",
      "name": "Thu Vũ",
      "avatar": "1711587724_0_76076.jpeg",
      "userId": 540308
    },
    "time_created": "09/05/2025 09:08",
    "replies": [],
    "table_info": {
      "id": "",
      "course_id": "",
      "name": "",
      "SEOurl": "",
      "course_url": ""
    },
    "comment_like": []
  }
}, {
  "id": 103574,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>わたしたち</p>",
    "word_stress": "<p><span style=\"text-decoration:overline\">わたし</span>たち</p>",
    "word_type": "Danh từ",
    "meaning": "<p>Ch&uacute;ng t&ocirc;i</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p>わたしたちはズンもりのがくせいです。</p>",
      "audio": null
    }],
    "meaning_example": ["<p>Ch&uacute;ng t&ocirc;i l&agrave; người Việt Nam</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 5,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-11T08:03:10.000000Z",
  "created_at": "2025-04-11T08:03:10.000000Z",
  "answers": [],
  "comment": {
    "id": 463322,
    "content": "25346666",
    "user_id": 540308,
    "count_like": 0,
    "created_at": "2025-04-21 11:24:47",
    "pin": 0,
    "parent_id": 0,
    "table_id": 103574,
    "table_name": "flashcard",
    "user_info": {
      "email": "<EMAIL>",
      "name": "Thu Vũ",
      "avatar": "1711587724_0_76076.jpeg",
      "userId": 540308
    },
    "time_created": "21/04/2025 11:24",
    "replies": [],
    "table_info": {
      "id": "",
      "course_id": "",
      "name": "",
      "SEOurl": "",
      "course_url": ""
    },
    "comment_like": []
  }
}, {
  "id": 103575,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>かれ</p>",
    "word_stress": null,
    "word_type": null,
    "meaning": "<p>Anh ấy, bạn trai</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p>かれはにほんじんですか。</p>",
      "audio": null
    }],
    "meaning_example": ["<p>Anh ấy l&agrave; người Nhật &agrave;?</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 6,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-11T08:04:15.000000Z",
  "created_at": "2025-04-11T08:04:15.000000Z",
  "answers": [],
  "comment": {
    "id": 463276,
    "content": "ggg",
    "user_id": 540309,
    "count_like": 0,
    "created_at": "2025-04-15 14:42:52",
    "pin": 0,
    "parent_id": 0,
    "table_id": 103575,
    "table_name": "flashcard",
    "user_info": {
      "email": "<EMAIL>",
      "name": "Dinhsuu",
      "avatar": "1711587724_0_76076.jpeg",
      "userId": 540309
    },
    "time_created": "15/04/2025 14:42",
    "replies": [],
    "table_info": {
      "id": "",
      "course_id": "",
      "name": "",
      "SEOurl": "",
      "course_url": ""
    },
    "comment_like": []
  }
}, {
  "id": 103576,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>あなた</p>",
    "word_stress": "<p>あ<span style=\"text-decoration:overline\">な</span>た</p>",
    "word_type": "Danh từ",
    "meaning": "<p>Anh/ chị/ &ocirc;ng/ b&agrave;, bạn (ng&ocirc;i thứ 2 số &iacute;t)</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p>あなたはベトナムじんですか。</p>",
      "audio": null
    }],
    "meaning_example": ["<p>Bạn l&agrave; người Việt Nam phải kh&ocirc;ng?</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 7,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-21T03:57:27.000000Z",
  "created_at": "2025-04-11T08:05:08.000000Z",
  "answers": [],
  "comment": {
    "id": 463320,
    "content": "hfh",
    "user_id": 540416,
    "count_like": 0,
    "created_at": "2025-04-21 10:58:39",
    "pin": 0,
    "parent_id": 0,
    "table_id": 103576,
    "table_name": "flashcard",
    "user_info": {
      "email": "<EMAIL>",
      "name": "2104",
      "avatar": "1711587724_0_76076.jpeg",
      "userId": 540416
    },
    "time_created": "21/04/2025 10:58",
    "replies": [],
    "table_info": {
      "id": "",
      "course_id": "",
      "name": "",
      "SEOurl": "",
      "course_url": ""
    },
    "comment_like": []
  }
}, {
  "id": 103577,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>おくさん</p>",
    "word_stress": null,
    "word_type": null,
    "meaning": "<p>Vợ (người kh&aacute;c)<br />lưu &yacute;: khi muốn hỏi hay giới thiệu vợ của ai đ&oacute; th&igrave; sử dụng từ n&agrave;y.</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p>かれのおくさんはズンもりの　せんせいです。</p>",
      "audio": null
    }],
    "meaning_example": ["<p>Vợ của anh ấy l&agrave; gi&aacute;o vi&ecirc;n dạy tiếng Nhật c&ugrave;ng trung t&acirc;m với t&ocirc;i</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 8,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-11T08:23:53.000000Z",
  "created_at": "2025-04-11T08:23:53.000000Z",
  "answers": [],
  "comment": null
}, {
  "id": 103578,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<p>どなた</p>",
    "word_stress": null,
    "word_type": null,
    "meaning": "<p>Vị n&agrave;o</p>",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<p>ゆかせんせいはどなたですか。</p>",
      "audio": null
    }],
    "meaning_example": ["<p>C&ocirc; Yuka l&agrave; ai thế?</p>"],
    "quiz_question": [],
    "kanji_meaning": null
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 9,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-11T08:24:51.000000Z",
  "created_at": "2025-04-11T08:24:51.000000Z",
  "answers": [],
  "comment": null
}, {
  "id": 103598,
  "lesson_id": 10847,
  "exam_part_id": null,
  "skill": 1,
  "mondai_id": null,
  "type": 17,
  "type_ld": null,
  "server": null,
  "video_name": null,
  "video_title": null,
  "value": {
    "word": "<ruby>私<rt>わたし</rt></ruby>たち",
    "word_stress": "<span style=\"text-decoration:overline\">わたし</span>たち",
    "word_type": "Danh từ",
    "meaning": "Chúng tôi",
    "audio": null,
    "front_image": null,
    "back_image": null,
    "example": [{
      "example": "<span style=\"color:#EF6D13\">わたしたち</span>はがくせいです。",
      "audio": null
    }],
    "meaning_example": ["<span style=\"color:#EF6D13\">Chúng tôi</span> là học sinh."],
    "quiz_question": [],
    "kanji_meaning": "Hoa"
  },
  "suggest": null,
  "explain": null,
  "explain_mp3": null,
  "value_data": null,
  "grade": "",
  "sort": 10,
  "show": 1,
  "is_quiz": 0,
  "video_full": 0,
  "updated_at": "2025-04-21T03:54:16.000000Z",
  "created_at": "2025-04-21T00:04:31.000000Z",
  "answers": [],
  "comment": {
    "id": 463321,
    "content": "123",
    "user_id": 540308,
    "count_like": 0,
    "created_at": "2025-04-21 11:24:28",
    "pin": 0,
    "parent_id": 0,
    "table_id": 103598,
    "table_name": "flashcard",
    "user_info": {
      "email": "<EMAIL>",
      "name": "Thu Vũ",
      "avatar": "1711587724_0_76076.jpeg",
      "userId": 540308
    },
    "time_created": "21/04/2025 11:24",
    "replies": [],
    "table_info": {
      "id": "",
      "course_id": "",
      "name": "",
      "SEOurl": "",
      "course_url": ""
    },
    "comment_like": []
  }
}];
/* harmony default export */ __webpack_exports__["default"] = ({
  components: {
    ProgressBar: _component_ProgressBar_vue__WEBPACK_IMPORTED_MODULE_0__["default"]
    // FlashCardModule
  },
  props: ["courseId"],
  name: "FlashCard",
  watch: {
    currentView: function currentView(newVal, oldVal) {
      console.log("currentView: ", newVal);
      if (newVal === TYPES_VIEW.STACKED_CARD) {
        this.initStackedCards();
      }
    }
  },
  data: function data() {
    return {
      listVocabulary: [],
      TYPES_VIEW: TYPES_VIEW,
      currentView: TYPES_VIEW.OVERVIEW,
      searchResults: searchResults,
      searchQuery: '',
      dataFlashCard: dataFlashcard,
      isJapanese: true
    };
  },
  created: function created() {
    this.getListVocabulary();
  },
  methods: {
    getListVocabulary: function getListVocabulary() {
      var _this = this;
      this.listVocabulary = this.listVocabulary.filter(function (item) {
        return item.course_id === _this.courseId;
      });
    },
    playAudio: function playAudio(card_id, audioUrl) {
      console.log("Play audio for card id: ".concat(card_id, ", url: ").concat(audioUrl));
    },
    toggleCommentTab: function toggleCommentTab() {
      var handle = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'open';
      var extraData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      console.log("mo tab cmt with handle: ".concat(handle));
      var currentCard = this.dataFlashcard[0];
      var eventData = _objectSpread({
        handle: handle,
        flashcard: currentCard
      }, extraData);
      this.$emit('open-comment-tab', eventData);
      var customEvent = new CustomEvent('open-comment-tab', {
        detail: eventData
      });
      document.dispatchEvent(customEvent);
    },
    initStackedCards: function initStackedCards() {
      var _this2 = this;
      this.$nextTick(function () {
        _this2.stackedCardsInstance = new _module_flashcard__WEBPACK_IMPORTED_MODULE_3__["default"]({
          visibleItems: 3,
          margin: 22,
          rotate: true,
          useOverlays: true
        });
      });
      this.updateCurrentFlashcard();
    },
    updateCurrentFlashcard: function updateCurrentFlashcard() {
      if (!this.stackedCardsInstance) return;
      var currentPos = this.stackedCardsInstance.currentPosition;
      var currentCard = this.dataFlashcard[currentPos];
      if (currentCard) {
        console.log('Current flashcard updated:', currentCard);

        // Tìm component Container và gọi phương thức setCurrentFlashcard
        var containerComponent = this.$parent.$children.find(function (child) {
          return child.$options.name === 'container';
        });
        if (containerComponent) {
          containerComponent.setCurrentFlashcard(currentCard);
        } else {
          console.error('Container component not found');
        }
      }
    }
  },
  mounted: function mounted() {
    // this.initStackedCards();
    // check window focus
    window.onfocus = function () {
      console.log('window is focused');
    };
    window.onblur = function () {
      console.log('window is blurred');
    };
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=template&id=1c837bb2&scoped=true":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=template&id=1c837bb2&scoped=true ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", [_c("div", {
    staticClass: "progress-bar",
    style: {
      height: _vm.height,
      background: _vm.background,
      width: _vm.widthPercent
    }
  }, [_c("div", {
    staticClass: "progress-bar-inner",
    style: {
      width: _vm.computedWidth,
      background: _vm.color
    }
  })])]);
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=template&id=5dd576c5&scoped=true":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=template&id=5dd576c5&scoped=true ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "flashcard-module relative"
  }, [_c("div", {
    staticClass: "stackedcards stackedcards--animatable a_cursor--pointer",
    attrs: {
      id: "stacked-cards-block"
    }
  }, [_c("div", {
    staticClass: "stackedcards-container",
    staticStyle: {
      "margin-bottom": "20px"
    }
  }, _vm._l(_vm.cards, function (card, index) {
    return _c("div", {
      key: card.id,
      staticClass: "card-item",
      "class": {
        "stackedcards-active": index === 0,
        "stackedcards-top": true,
        "stackedcards--animatable": true,
        "stackedcards-origin-top": true
      },
      attrs: {
        "data-id": card.id
      }
    }, [_c("div", {
      staticClass: "card-inner",
      "class": {
        flip: !_vm.isJapanese
      },
      attrs: {
        "data-id": card.id
      }
    }, [_c("div", {
      staticClass: "card__face card__face--jp card__face--front"
    }, [_c("div", {
      staticClass: "card-wrap p-4 h-[90%] overflow-y-auto relative"
    }, [_c("div", {
      staticClass: "card_header flex items-center"
    }, [card.value && card.value.audio ? _c("div", {
      staticClass: "card_audio noFlip mr-2 cursor-pointer",
      on: {
        click: function click($event) {
          return _vm.playAudio(card.value.audio);
        }
      }
    }, [_c("svg", {
      attrs: {
        width: "34",
        height: "34",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }
    }, [_c("path", {
      attrs: {
        d: "M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",
        fill: "#4E87FF"
      }
    })])]) : _vm._e(), _vm._v(" "), _c("div", {
      staticClass: "card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]",
      domProps: {
        innerHTML: _vm._s(card.value && card.value.word_stress)
      }
    })]), _vm._v(" "), _c("div", {
      staticClass: "card_content min-h-[calc(100%-34px)] flex flex-col"
    }, [_c("div", {
      staticClass: "content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex",
      staticStyle: {
        "flex-grow": "1"
      },
      domProps: {
        innerHTML: _vm._s(card.value && card.value.word)
      }
    }), _vm._v(" "), card.value && card.value.front_image ? _c("div", {
      staticClass: "content-img text-center p-[40px]"
    }, [_c("img", {
      attrs: {
        src: "https://video-test.dungmori.com/images/".concat(card.value.front_image)
      }
    })]) : _vm._e(), _vm._v(" "), card.value && card.value.example && card.value.example.length ? _c("div", {
      staticClass: "example-wrap"
    }, [_c("div", {
      staticClass: "example-title font-beanbag-medium text-[#757575] text-xl"
    }, [_vm._v("\n                      Ví dụ:\n                    ")]), _vm._v(" "), _vm._l(card.value.example, function (example, i) {
      return _c("div", {
        key: i,
        staticClass: "example-content font-gen-jyuu-gothic-medium text-[#07403F] text-2xl"
      }, [_c("div", {
        domProps: {
          innerHTML: _vm._s(example)
        }
      })]);
    })], 2) : _vm._e()])])]), _vm._v(" "), _c("div", {
      staticClass: "card__face card__face--vi card__face--back"
    }, [_c("div", {
      staticClass: "card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative"
    }, [_c("div", {
      staticClass: "card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5",
      staticStyle: {
        "flex-grow": "1"
      }
    }, [_c("div", {
      staticClass: "text-center",
      domProps: {
        innerHTML: _vm._s(card.value && card.value.meaning)
      }
    })]), _vm._v(" "), _c("div", {
      staticClass: "font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]",
      domProps: {
        innerHTML: _vm._s(card.value && card.value.kanji_meaning)
      }
    }), _vm._v(" "), card.value && card.value.back_image ? _c("div", {
      staticClass: "card_img_back p-[40px] content-img text-center"
    }, [_c("img", {
      attrs: {
        src: "https://video-test.dungmori.com/images/".concat(card.value.back_image)
      }
    })]) : _vm._e(), _vm._v(" "), card.value && card.value.meaning_example && card.value.meaning_example.length ? _c("div", {
      staticClass: "example-wrap"
    }, [_c("div", {
      staticClass: "example-title font-beanbag-medium text-[#757575] text-xl"
    }, [_vm._v("\n                    Ví dụ:\n                  ")]), _vm._v(" "), _vm._l(card.value.meaning_example, function (example, i) {
      return _c("div", {
        key: i,
        staticClass: "example-content font-beanbag-medium text-[#07403F] text-2xl"
      }, [_c("div", {
        domProps: {
          innerHTML: _vm._s(example)
        }
      })]);
    })], 2) : _vm._e()])])])]);
  }), 0), _vm._v(" "), _c("div", {
    staticClass: "stackedcards-overlay top"
  }), _vm._v(" "), _vm._m(0), _vm._v(" "), _vm._m(1)])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "stackedcards-overlay right"
  }, [_c("span", {
    staticClass: "stackedcards-overlay-img"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "stackedcards-overlay left"
  }, [_c("span", {
    staticClass: "stackedcards-overlay-img"
  })]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/course/components/SearchBar.vue?vue&type=template&id=4a58c6b8":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/course/components/SearchBar.vue?vue&type=template&id=4a58c6b8 ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "flex items-center"
  }, [_c("button", {
    staticClass: "flex-none flex items-center justify-between border-none bg-[#F5F5F5] w-[85px] h-[40px] px-4 rounded-full",
    on: {
      click: _vm.hideMenu
    }
  }, [_c("img", {
    staticClass: "h-[14px]",
    attrs: {
      src: "/images/icons/hide.png",
      alt: "hide.png"
    }
  }), _vm._v(" "), _c("span", {
    staticClass: "text-sm font-averta-regular"
  }, [_vm._v("Ẩn")])]), _vm._v(" "), _vm.currentTab != "comment" ? _c("div", {
    staticClass: "relative w-full ml-5"
  }, [_c("input", {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.searchField,
      expression: "searchField"
    }],
    staticClass: "pl-4 pr-8 text-[#1E1E1E] text-sm font-averta-regular placeholder:text-[#757575] bg-[#F5F5F5] w-full rounded-full h-[40px] w-full sp:hidden",
    attrs: {
      type: "text",
      placeholder: "Tìm kiếm bài học với từ khoá bất kì"
    },
    domProps: {
      value: _vm.searchField
    },
    on: {
      input: function input($event) {
        if ($event.target.composing) return;
        _vm.searchField = $event.target.value;
      }
    }
  }), _vm._v(" "), _c("input", {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.searchField,
      expression: "searchField"
    }],
    staticClass: "pl-4 pr-8 text-[#1E1E1E] text-sm font-averta-regular placeholder:text-[#757575] bg-[#F5F5F5] w-full rounded-full h-[40px] w-full desktop:hidden",
    attrs: {
      type: "text",
      placeholder: "Tìm kiếm bài học"
    },
    domProps: {
      value: _vm.searchField
    },
    on: {
      input: function input($event) {
        if ($event.target.composing) return;
        _vm.searchField = $event.target.value;
      }
    }
  }), _vm._v(" "), _c("div"), _vm._v(" "), _c("img", {
    staticClass: "absolute top-1/2 right-4 transform -translate-y-1/2 w-[20px] h-[20px]",
    "class": "".concat(_vm.searchField.length > 0 ? "block" : "hidden"),
    attrs: {
      src: "/images/icons/clear.png",
      alt: "clear.png"
    },
    on: {
      click: function click($event) {
        _vm.searchField = "";
      }
    }
  }), _vm._v(" "), _c("img", {
    staticClass: "absolute top-1/2 right-4 transform -translate-y-1/2 w-[20px] h-[20px]",
    "class": "".concat(_vm.searchField.length === 0 ? "block" : "hidden"),
    attrs: {
      src: "/images/icons/search.png",
      alt: "search.png"
    }
  })]) : _vm._e()]);
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=template&id=5f98c1fb&scoped=true":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=template&id=5f98c1fb&scoped=true ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "vocabulary-container"
  }, [_c("div", {
    staticClass: "nav-menu",
    attrs: {
      id: "navMenu"
    }
  }, [_c("div", {
    staticClass: "nav-menu-header"
  }, [_c("div", {
    staticClass: "nav-menu-title cursor-pointer",
    on: {
      click: function click($event) {
        _vm.currentView = _vm.typesView.OVERVIEW;
      }
    }
  }, [_vm._v("Từ vựng")]), _vm._v(" "), _vm._m(0)]), _vm._v(" "), _c("div", {
    staticClass: "nav-menu-items"
  }, [_vm._m(1), _vm._v(" "), _c("div", {
    staticClass: "nav-submenu",
    attrs: {
      id: "submenu1"
    }
  }, _vm._l(_vm.list_vocabulary.jlpt.data, function (vocabulary_jlpt, index) {
    return _c("div", {
      staticClass: "nav-submenu-item font-beanbag-regular text-[18px] text-black",
      on: {
        click: function click($event) {
          return _vm.openCourseFlashCardDetail(vocabulary_jlpt.id);
        }
      }
    }, [_c("span", {
      staticClass: "text-black"
    }, [_vm._v(_vm._s(vocabulary_jlpt.name))])]);
  }), 0), _vm._v(" "), _vm._m(2), _vm._v(" "), _c("div", {
    staticClass: "nav-submenu",
    attrs: {
      id: "submenu2"
    }
  }, _vm._l(_vm.list_vocabulary.specialized.data, function (vocabulary_specialized, index) {
    return _c("div", {
      staticClass: "nav-submenu-item font-beanbag-regular text-[18px] text-black"
    }, [_c("span", {
      staticClass: "text-black"
    }, [_vm._v(_vm._s(vocabulary_specialized.name))])]);
  }), 0), _vm._v(" "), _c("div", {
    staticClass: "nav-menu-item justify-between",
    on: {
      click: function click($event) {
        _vm.currentView = _vm.typesView.FAVORITE;
      }
    }
  }, [_c("span", {
    staticClass: "nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]"
  }, [_vm._v("Yêu thích")]), _vm._v(" "), _c("i", {
    staticClass: "fas fa-heart text-[#FF7C79]"
  })])])]), _vm._v(" "), _c("div", {
    staticClass: "content-area"
  }, [_c("div", {
    staticClass: "content-section active max-w-[1096px] mx-auto mt-[58px]",
    attrs: {
      id: "default"
    }
  }, [_vm.currentView !== _vm.typesView.FAVORITE && _vm.currentView !== _vm.typesView.SPECIALIZED ? _c("div", {
    staticClass: "vocabulary-search-container"
  }, [_vm.currentView === _vm.typesView.SEARCH ? _c("div", {
    staticClass: "vocabulary-search-btn-back h-[80px] w-[80px]",
    on: {
      click: function click($event) {
        _vm.currentView = _vm.typesView.OVERVIEW;
      }
    }
  }, [_c("i", {
    staticClass: "fas fa-arrow-left"
  })]) : _vm._e(), _vm._v(" "), _c("div", {
    staticClass: "vocabulary-search-bar flex items-center rounded-full bg-white shadow-md h-[80px] px-[20px] transition-all duration-300 flex-1 min-w-0"
  }, [_vm._m(3), _vm._v(" "), _c("input", {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.searchQuery,
      expression: "searchQuery"
    }],
    staticClass: "search-input",
    attrs: {
      type: "text",
      placeholder: "Nhập từ vựng muốn tìm kiếm"
    },
    domProps: {
      value: _vm.searchQuery
    },
    on: {
      focus: function focus($event) {
        _vm.searchFocused = true;
      },
      blur: function blur($event) {
        _vm.searchFocused = false;
      },
      input: function input($event) {
        if ($event.target.composing) return;
        _vm.searchQuery = $event.target.value;
      }
    }
  }), _vm._v(" "), _vm.searchQuery ? _c("div", {
    staticClass: "search-clear cursor-pointer bg-[#757575] px-[10px] py-[5px] rounded-full",
    on: {
      click: function click($event) {
        _vm.searchQuery = "";
      }
    }
  }, [_c("i", {
    staticClass: "fas fa-times"
  })]) : _vm._e()])]) : _vm._e(), _vm._v(" "), _vm.currentView === _vm.typesView.OVERVIEW ? [_c("div", {
    staticClass: "section-content overview-content"
  }, [_c("div", {
    staticClass: "chart-overview-container flex mb-[66px]"
  }, [_c("div", {
    staticClass: "chart-container w-2/3"
  }, [_c("div", {
    staticClass: "text-right"
  }, [_c("button", {
    staticClass: "btn-info-chart bg-[#FFB98F] text-white rounded-full px-[12px]",
    on: {
      click: function click($event) {
        _vm.showDialogInfo = true;
      }
    }
  }, [_c("span", {
    staticClass: "text-[20px] font-beanbag-medium"
  }, [_vm._v("i")])])]), _vm._v(" "), _c("div", {
    staticClass: "html-chart"
  }, [_c("div", {
    staticClass: "chart-columns"
  }, [_c("div", {
    staticClass: "chart-column",
    style: {
      height: _vm.chartData.learned ? _vm.getBarHeight(_vm.chartData.learned) : "0%"
    }
  }, [_c("div", {
    staticClass: "column-value font-beanbag-medium"
  }, [_vm._v(_vm._s(_vm.chartData.learned) + " từ")]), _vm._v(" "), _vm._m(4), _vm._v(" "), _c("div", {
    staticClass: "column-label"
  }, [_vm._v("Đã học")])]), _vm._v(" "), _c("div", {
    staticClass: "chart-column",
    style: {
      height: _vm.chartData.temporary ? _vm.getBarHeight(_vm.chartData.temporary) : "0%"
    }
  }, [_c("div", {
    staticClass: "column-value font-beanbag-medium"
  }, [_vm._v(_vm._s(_vm.chartData.temporary) + " từ")]), _vm._v(" "), _vm._m(5), _vm._v(" "), _c("div", {
    staticClass: "column-label"
  }, [_vm._v("Tạm nhớ")])]), _vm._v(" "), _c("div", {
    staticClass: "chart-column",
    style: {
      height: _vm.chartData.memorized ? _vm.getBarHeight(_vm.chartData.memorized) : "0%"
    }
  }, [_c("div", {
    staticClass: "column-value font-beanbag-medium"
  }, [_vm._v(_vm._s(_vm.chartData.memorized) + " từ")]), _vm._v(" "), _vm._m(6), _vm._v(" "), _c("div", {
    staticClass: "column-label"
  }, [_vm._v("Ghi nhớ")])]), _vm._v(" "), _c("div", {
    staticClass: "chart-column",
    style: {
      height: _vm.chartData.mastered ? _vm.getBarHeight(_vm.chartData.mastered) : "0%"
    }
  }, [_c("div", {
    staticClass: "column-value font-beanbag-medium"
  }, [_vm._v(_vm._s(_vm.chartData.mastered) + " từ")]), _vm._v(" "), _vm._m(7), _vm._v(" "), _c("div", {
    staticClass: "column-label"
  }, [_vm._v("Thuộc lòng")])])])])]), _vm._v(" "), _c("div", {
    staticClass: "chart-header w-1/3"
  }, [_c("div", {
    staticClass: "flex justify-between items-center border-b-[1px] pb-3",
    staticStyle: {
      "border-bottom-style": "dashed",
      "border-bottom-color": "#176867"
    }
  }, [_c("div", {
    staticClass: "chart-title font-beanbag-medium text-[20px] text-[#176867]"
  }, [_vm._v("Tổng số từ vựng")]), _vm._v(" "), _c("div", {
    staticClass: "total-word rounded-full text-[20px] font-beanbag-medium text-[#176867] px-[15px] py-[2px] bg-[#CEFFD8]"
  }, [_vm._v("\n                  " + _vm._s(_vm.totalWords) + "\n                ")])]), _vm._v(" "), _c("div", {
    staticClass: "chart-filters pt-3"
  }, [_c("div", {
    staticClass: "filter-checkboxes overflow-y-auto h-[330px]"
  }, _vm._l(_vm.categories, function (category, index) {
    return _c("label", {
      key: index,
      staticClass: "checkbox-container"
    }, [_c("input", {
      directives: [{
        name: "model",
        rawName: "v-model",
        value: category.selected,
        expression: "category.selected"
      }],
      attrs: {
        type: "checkbox"
      },
      domProps: {
        checked: Array.isArray(category.selected) ? _vm._i(category.selected, null) > -1 : category.selected
      },
      on: {
        change: function change($event) {
          var $$a = category.selected,
            $$el = $event.target,
            $$c = $$el.checked ? true : false;
          if (Array.isArray($$a)) {
            var $$v = null,
              $$i = _vm._i($$a, $$v);
            if ($$el.checked) {
              $$i < 0 && _vm.$set(category, "selected", $$a.concat([$$v]));
            } else {
              $$i > -1 && _vm.$set(category, "selected", $$a.slice(0, $$i).concat($$a.slice($$i + 1)));
            }
          } else {
            _vm.$set(category, "selected", $$c);
          }
        }
      }
    }), _vm._v(" "), _c("span", {
      staticClass: "checkbox-label font-beanbag-medium font-medium"
    }, [_vm._v(_vm._s(category.name))])]);
  }), 0)])])]), _vm._v(" "), _c("div", {
    staticClass: "vocabulary-section",
    attrs: {
      id: "section-jlpt"
    }
  }, [_vm._m(8), _vm._v(" "), _c("div", {
    staticClass: "vocabulary-cards"
  }, _vm._l(_vm.list_vocabulary.jlpt.data, function (item, index) {
    return _c("div", {
      key: index,
      staticClass: "vocabulary-card",
      "class": item.status === "incoming" ? "vocabulary-card-incoming" : "",
      attrs: {
        "data-key": index
      }
    }, [_c("div", {
      staticClass: "vocabulary-card-content"
    }, [item.status === "new" ? _c("div", {
      staticClass: "vocabulary-card-status"
    }, [_c("span", {
      staticClass: "badge badge-new font-gen-jyuu-gothic-medium text-[12px]"
    }, [_vm._v("NEW")])]) : _vm._e(), _vm._v(" "), _c("div", {
      staticClass: "vocabulary-line bg-[#57D061]"
    }), _vm._v(" "), _c("div", {
      staticClass: "vocabulary-card-title font-gen-jyuu-gothic-medium text-bold text-[20px]"
    }, [_vm._v(_vm._s(item.name) + "\n                  ")]), _vm._v(" "), _c("div", {
      staticClass: "vocabulary-card-count font-gen-jyuu-gothic text-[20px]"
    }, [_vm._v("\n                    " + _vm._s(item.word_count) + " từ vựng\n                  ")])]), _vm._v(" "), item.status !== "incoming" ? _c("div", {
      staticClass: "vocabulary-card-name font-zuume-semibold"
    }, [_c("h1", [_vm._v("\n                    " + _vm._s(item.level) + "\n                  ")])]) : _c("div", {
      staticClass: "vocabulary-card-incoming-text"
    }, [_c("p", {
      staticClass: "text-center text-[23px] font-gen-jyuu-gothic-medium text-[#757575]"
    }, [_vm._v("Sắp ra mắt")])])]);
  }), 0)]), _vm._v(" "), _c("div", {
    staticClass: "vocabulary-section",
    attrs: {
      id: "section-specialized"
    }
  }, [_vm._m(9), _vm._v(" "), _c("div", {
    staticClass: "vocabulary-cards"
  }, _vm._l(_vm.list_vocabulary.specialized.data, function (item, index) {
    return _c("div", {
      key: index,
      staticClass: "vocabulary-card",
      "class": item.status === "incoming" ? "vocabulary-card-incoming" : "",
      attrs: {
        "data-key": index
      }
    }, [_c("div", {
      staticClass: "vocabulary-card-content"
    }, [item.status === "hot" ? _c("div", {
      staticClass: "vocabulary-card-status"
    }, [_c("span", {
      staticClass: "badge badge-new font-gen-jyuu-gothic-medium text-[12px]"
    }, [_vm._v("HOT")])]) : _vm._e(), _vm._v(" "), _c("div", {
      staticClass: "vocabulary-line bg-[#4E87FF]"
    }), _vm._v(" "), _c("div", {
      staticClass: "vocabulary-card-title font-gen-jyuu-gothic-medium text-bold text-[20px] leading-[20px]"
    }, [_vm._v("\n                    " + _vm._s(item.name) + "\n                  ")]), _vm._v(" "), _c("div", {
      staticClass: "vocabulary-card-count font-gen-jyuu-gothic text-[20px]"
    }, [_vm._v("\n                    " + _vm._s(item.word_count) + " từ vựng\n                  ")])]), _vm._v(" "), item.status !== "incoming" ? _c("img", {
      staticClass: "vocabulary-card-image",
      attrs: {
        src: "/images/vocabulary/test.png",
        alt: item.name
      }
    }) : _c("div", {
      staticClass: "vocabulary-card-incoming-text"
    }, [_c("p", {
      staticClass: "text-center text-[23px] font-gen-jyuu-gothic-medium text-[#757575]"
    }, [_vm._v("Sắp ra mắt")])])]);
  }), 0)]), _vm._v(" "), _c("div", {
    staticClass: "vocabulary-section",
    attrs: {
      id: "section-favorite"
    }
  }, [_vm._m(10), _vm._v(" "), _vm._l(_vm.list_vocabulary.favorite.data, function (item, index) {
    return _c("div", {
      key: index,
      staticClass: "vocabulary-card card-favorite",
      attrs: {
        "data-key": index
      }
    }, [_c("div", {
      staticClass: "vocabulary-card-content-favorite"
    }, [_c("div", {
      staticClass: "absolute h-[50px] bg-[#FF7C79] top-[40px] left-[25px] w-[3px] bg-[#FF7C79] rounded-full"
    }), _vm._v(" "), _c("div", {
      staticClass: "w-full flex justify-between"
    }, [_c("div", {
      staticClass: "flex flex-col justify-around"
    }, [_c("div", {
      staticClass: "pt-[23px] pl-[32px] pb-[13px]"
    }, [_c("div", {
      staticClass: "text-[28px] font-gen-jyuu-gothic-medium text-bold"
    }, [_vm._v("\n                        " + _vm._s(item.name) + "\n                      ")]), _vm._v(" "), _c("div", {
      staticClass: "vocabulary-card-count font-gen-jyuu-gothic text-[20px]"
    }, [_vm._v("\n                        " + _vm._s(item.word_count) + " từ vựng\n                      ")])]), _vm._v(" "), _vm._m(11, true)]), _vm._v(" "), _c("div", [_c("img", {
      staticClass: "h-full object-cover",
      attrs: {
        src: "/images/vocabulary/bg-favorite-overview.svg",
        alt: item.name
      }
    })])])])]);
  })], 2)])] : _vm._e(), _vm._v(" "), _vm.currentView === _vm.typesView.SEARCH ? [_c("div", {
    staticClass: "section-content search-content"
  }, [_vm.searchResults.length > 0 ? _c("div", {
    staticClass: "search-result"
  }, [_c("div", {
    staticClass: "search-result-items"
  }, _vm._l(_vm.searchResults, function (item, index) {
    return _c("div", {
      key: index,
      staticClass: "search-result-item"
    }, [_c("div", {
      staticClass: "search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] mb-3 pb-3"
    }, [_c("div", {
      staticClass: "search-result-item-title font-gen-jyuu-gothic-medium text-bold text-black text-[20px]"
    }, [_vm._v("\n                    " + _vm._s(item.word) + "\n                  ")]), _vm._v(" "), _c("div", {
      staticClass: "search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]"
    }, [_vm._v("\n                    " + _vm._s(item.description) + "\n                  ")]), _vm._v(" "), _c("div", {
      staticClass: "search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1"
    }, [_vm._v("\n                    " + _vm._s(item.specialized) + "\n                  ")])])]);
  }), 0)]) : _c("div", {
    staticClass: "search-not-found"
  }, [_vm._m(12), _vm._v(" "), _vm.searchQuery ? _c("div", {
    staticClass: "search-not-found-text"
  }, [_vm._v('\n              Không tìm thấy kết quả nào cho "' + _vm._s(_vm.searchQuery) + '"\n            ')]) : _c("div", {
    staticClass: "search-not-found-text"
  }, [_vm._v("\n              Nhập từ vựng bạn muốn tìm kiếm\n            ")])])])] : _vm._e(), _vm._v(" "), _vm.currentView === _vm.typesView.SPECIALIZED ? [_c("div", {
    staticClass: "section-content specialized-content"
  }, [_c("flash-card", {
    attrs: {
      "course-id": _vm.courseSpecializedActive
    }
  })], 1)] : _vm._e(), _vm._v(" "), _vm.currentView === _vm.typesView.FAVORITE ? [_c("div", {
    staticClass: "section-content favorite-content"
  }, [_c("div", {
    staticClass: "flex justify-between items-center mb-10"
  }, [_c("div", {
    staticClass: "flex items-center"
  }, [_c("div", {
    staticClass: "vocabulary-search-btn-back h-[65px] w-[65px]",
    on: {
      click: function click($event) {
        _vm.currentView = _vm.typesView.OVERVIEW;
      }
    }
  }, [_c("i", {
    staticClass: "fas fa-arrow-left"
  })]), _vm._v(" "), _vm._m(13)]), _vm._v(" "), _c("div", {
    staticClass: "vocabulary-favorite-search-bar flex items-center rounded-full bg-white shadow-md h-[65px] px-[20px] transition-all duration-300 flex-1 min-w-0 max-w-[400px]"
  }, [_vm._m(14), _vm._v(" "), _c("input", {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.searchQuery,
      expression: "searchQuery"
    }],
    staticClass: "search-input leading-[65px]",
    attrs: {
      type: "text",
      placeholder: "Nhập từ vựng muốn tìm kiếm"
    },
    domProps: {
      value: _vm.searchQuery
    },
    on: {
      input: function input($event) {
        if ($event.target.composing) return;
        _vm.searchQuery = $event.target.value;
      }
    }
  })])]), _vm._v(" "), _vm._m(15), _vm._v(" "), _vm.searchResults.length > 0 ? _c("div", {
    staticClass: "search-result list-favorite"
  }, [_c("div", {
    staticClass: "search-result-items"
  }, _vm._l(_vm.searchResults, function (item, index) {
    return _c("div", {
      key: index,
      staticClass: "search-result-item cursor-pointer",
      on: {
        click: function click($event) {
          return _vm.openFlashcardPopup(index);
        }
      }
    }, [_c("div", {
      staticClass: "search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] mb-3 pb-3"
    }, [_c("div", {
      staticClass: "search-result-item-title font-gen-jyuu-gothic-medium text-bold text-black text-[20px]"
    }, [_vm._v("\n                    " + _vm._s(item.word) + "\n                  ")]), _vm._v(" "), _c("div", {
      staticClass: "search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]"
    }, [_vm._v("\n                    " + _vm._s(item.description) + "\n                  ")]), _vm._v(" "), _c("div", {
      staticClass: "search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1"
    }, [_vm._v("\n                    " + _vm._s(item.specialized) + "\n                  ")])])]);
  }), 0)]) : _vm._e()])] : _vm._e()], 2)]), _vm._v(" "), _c("el-dialog", {
    staticClass: "dialog-vocabulary-info",
    attrs: {
      visible: _vm.showDialogInfo,
      "max-width": "1097px",
      top: "8vh",
      width: "80%",
      height: "80vh",
      center: "",
      "show-close": false,
      "close-on-click-modal": true
    },
    on: {
      "update:visible": function updateVisible($event) {
        _vm.showDialogInfo = $event;
      }
    },
    scopedSlots: _vm._u([{
      key: "title",
      fn: function fn() {
        return [_c("div", {
          staticClass: "custom-title pt-[29px] pl-[43px] pr-[38px] flex justify-between items-end rounded-[32px]"
        }, [_c("div", {
          staticClass: "flex items-center gap-2"
        }, [_c("div", {
          staticClass: "text-[#073A3B] font-beanbag text-[24px] bg-[#CCF8D1] px-5 py-1 rounded-full"
        }, [_vm._v("\n            Rừng ngôn từ\n          ")]), _vm._v(" "), _c("div", {
          staticClass: "text-[#0C403F] text-[27px]"
        }, [_vm._v("\n            言葉の森\n          ")])]), _vm._v(" "), _c("el-button", {
          attrs: {
            icon: "el-icon-close",
            circle: ""
          },
          on: {
            click: function click($event) {
              _vm.showDialogInfo = false;
            }
          }
        })], 1)];
      },
      proxy: true
    }])
  }, [_vm._v(" "), _c("div", {
    staticClass: "dialog-wrapper overflow-y-scroll h-[60vh] pl-[43px] py-[12px] pr-[38px] mx-[43px] my-[12px] custom-scrollbar"
  }, [_c("div", {
    staticClass: "font-beanbag-regular text-[#07403F] text-[20px] mb-3 break-normal"
  }, [_vm._v("\n        Là nơi thống kê chi tiết số lượng từ vựng nằm ở các "), _c("span", {
    staticClass: "text-italic font-beanbag-medium break-normal"
  }, [_vm._v("mức độ ghi nhớ")]), _vm._v("\n        khác nhau thông qua phương pháp học "), _c("span", {
    staticClass: "text-italic font-beanbag-medium break-normal"
  }, [_vm._v("Lặp lại ngắt quãng (Spaced Repetition)")]), _vm._v(",\n        giúp bạn hoàn toàn làm chủ quá trình học của bản thân!\n      ")]), _vm._v(" "), _c("div", {
    staticClass: "text-center mb-5"
  }, [_c("img", {
    attrs: {
      src: "/images/vocabulary/img-popup-info.svg",
      alt: "Thông tin"
    }
  })]), _vm._v(" "), _c("div", {
    staticClass: "mb-5"
  }, [_c("ul", {
    staticClass: "list-disc font-beanbag-regular text-[#07403F] text-[20px]"
  }, [_c("li", {
    staticClass: "py-[5px]",
    staticStyle: {
      "list-style": "disc"
    }
  }, [_c("span", {
    staticClass: "font-beanbag-medium bg-[#FFD1B0] rounded-[8px]"
  }, [_vm._v("Đã học")]), _vm._v(":\n            Những từ bạn mới được học.\n          ")]), _vm._v(" "), _c("li", {
    staticClass: "py-[5px]",
    staticStyle: {
      "list-style": "disc"
    }
  }, [_c("span", {
    staticClass: "font-beanbag-medium bg-[#FFEF94] rounded-[8px]"
  }, [_vm._v("Tạm nhớ")]), _vm._v(":\n            Những từ bạn vẫn nhớ sau 1 thời gian ngắn không sử dụng tới.\n          ")]), _vm._v(" "), _c("li", {
    staticClass: "py-[5px]",
    staticStyle: {
      "list-style": "disc"
    }
  }, [_c("span", {
    staticClass: "font-beanbag-medium bg-[#D0FCA1] rounded-[8px]"
  }, [_vm._v("Ghi nhớ")]), _vm._v(":\n            Từ vựng đã được lưu vào trí nhớ dài hạn của bạn rồi!\n          ")]), _vm._v(" "), _c("li", {
    staticClass: "py-[5px]",
    staticStyle: {
      "list-style": "disc"
    }
  }, [_c("span", {
    staticClass: "font-beanbag-medium bg-[#80FBA6] rounded-[8px]"
  }, [_vm._v("Thuộc lòng")]), _vm._v(":\n            Tuyệt vời! Những từ vựng này giờ là của bạn!\n          ")])])]), _vm._v(" "), _c("div", {
    staticClass: "font-beanbag-regular text-[#07403F] text-[20px]"
  }, [_vm._v("\n        Chăm chỉ học tập, bạn sẽ có được một "), _c("span", {
    staticClass: "font-beanbag-medium italic"
  }, [_vm._v("Rừng ngôn từ")]), _vm._v(" thật phong\n        phú, đa dạng! Cùng học bạn nhé!\n      ")]), _vm._v(" "), _c("div", {
    staticClass: "flex justify-center mt-5"
  }, [_c("button", {
    staticClass: "px-12 py-4 flex items-center justify-center bg-[#57D061] rounded-full cursor-pointer drop-shadow",
    on: {
      click: function click($event) {
        _vm.showDialogInfo = false;
      }
    }
  }, [_c("span", {
    staticClass: "text-btn font-beanbag-medium text-[#07403F] text-xl mr-1 text-[20px]"
  }, [_vm._v("\n            Mình đã hiểu!\n          ")])])])])]), _vm._v(" "), _vm.showFlashcardPopup ? _c("div", {
    staticClass: "flashcard-popup-overlay",
    on: {
      click: function click($event) {
        if ($event.target !== $event.currentTarget) return null;
        return _vm.closeFlashcardPopup.apply(null, arguments);
      }
    }
  }, [_c("div", {
    staticClass: "flashcard-popup"
  }, [_c("div", {
    staticClass: "flashcard-popup-content"
  }, [_c("button", {
    staticClass: "flashcard-nav-button prev",
    attrs: {
      disabled: _vm.currentFlashcardIndex === 0
    },
    on: {
      click: _vm.prevFlashcard
    }
  }, [_c("i", {
    staticClass: "fas fa-chevron-left"
  })]), _vm._v(" "), _c("div", {
    staticClass: "flashcards-wrap cursor-pointer",
    staticStyle: {
      width: "600px"
    }
  }, _vm._l(_vm.cards, function (card, index) {
    return _c("div", {
      directives: [{
        name: "show",
        rawName: "v-show",
        value: index === _vm.currentFlashcardIndex,
        expression: "index === currentFlashcardIndex"
      }],
      key: card.id,
      staticClass: "card-item",
      "class": {
        "stackedcards-active": index === 0,
        "stackedcards-top": true,
        "stackedcards--animatable": true,
        "stackedcards-origin-top": true
      },
      attrs: {
        "data-id": card.id
      }
    }, [_c("div", {
      staticClass: "card-inner",
      attrs: {
        id: "card-inner-".concat(card.id)
      },
      on: {
        click: function click($event) {
          return _vm.flipCard(card.id, $event);
        }
      }
    }, [_c("div", {
      staticClass: "card__face card__face--jp card__face--front"
    }, [_c("div", {
      staticClass: "card-wrap p-4 h-[90%] overflow-y-auto relative"
    }, [_c("div", {
      staticClass: "card_header flex items-center"
    }, [_c("div", {
      staticClass: "card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer",
      on: {
        click: function click($event) {
          return _vm.playAudio("audio");
        }
      }
    }, [_c("svg", {
      staticClass: "noFlip",
      attrs: {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }
    }, [_c("path", {
      attrs: {
        d: "M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",
        fill: "#4E87FF"
      }
    }), _vm._v(" "), _c("path", {
      attrs: {
        d: "M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",
        fill: "#4E87FF"
      }
    }), _vm._v(" "), _c("path", {
      attrs: {
        opacity: "0.4",
        d: "M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",
        fill: "#4E87FF"
      }
    }), _vm._v(" "), _c("path", {
      attrs: {
        d: "M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",
        fill: "#4E87FF"
      }
    })])]), _vm._v(" "), _c("div", {
      staticClass: "card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]",
      domProps: {
        innerHTML: _vm._s(card.value.word_stress)
      }
    })]), _vm._v(" "), _c("div", {
      staticClass: "card_content min-h-[calc(100%-34px)] flex flex-col"
    }, [_c("div", {
      staticClass: "content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex",
      staticStyle: {
        "flex-grow": "1"
      },
      domProps: {
        innerHTML: _vm._s(card.value.word)
      }
    }), _vm._v(" "), card.value.front_image ? _c("div", {
      staticClass: "content-img text-center p-[40px]"
    }, [_c("img", {
      attrs: {
        src: "https://video-test.dungmori.com/images/".concat(card.value.front_image)
      }
    })]) : _vm._e(), _vm._v(" "), card.value.example.length ? _c("div", {
      staticClass: "example-wrap"
    }, [_c("p", {
      staticClass: "w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"
    }, [_vm._v("\n                        Ví dụ\n                      ")]), _vm._v(" "), _c("div", {
      staticClass: "list-example"
    }, [_vm._l(card.value.example, function (example, index) {
      return [_c("div", {
        staticClass: "example-item mb-1"
      }, [example.audio ? _c("svg", {
        staticClass: "w-[36px] h-[36px] noFlip cursor-pointer",
        attrs: {
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        },
        on: {
          click: function click($event) {
            return _vm.playAudio(card.id + "_example_" + index, example.audio);
          }
        }
      }, [_c("path", {
        attrs: {
          d: "M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",
          fill: "#4E87FF"
        }
      }), _vm._v(" "), _c("path", {
        attrs: {
          d: "M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",
          fill: "#4E87FF"
        }
      }), _vm._v(" "), _c("path", {
        attrs: {
          opacity: "0.4",
          d: "M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",
          fill: "#4E87FF"
        }
      }), _vm._v(" "), _c("path", {
        attrs: {
          d: "M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",
          fill: "#4E87FF"
        }
      })]) : _vm._e(), _vm._v(" "), _c("div", {
        staticClass: "ml-2 font-beanbag-regular text-2xl flex items-start text-balance"
      }, [_vm._v("\n                              " + _vm._s(index + 1) + ". "), _c("span", {
        staticClass: "ml-2",
        domProps: {
          innerHTML: _vm._s(example.example)
        }
      })])])];
    })], 2)]) : _vm._e()])]), _vm._v(" "), _c("div", {
      staticClass: "card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]"
    }, [_c("svg", {
      staticClass: "m-3 noFlip",
      attrs: {
        width: "33",
        height: "28",
        viewBox: "0 0 33 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }
    }, [_c("path", {
      attrs: {
        d: "M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z",
        fill: "#FF7C79",
        stroke: "#FF7C79"
      }
    })])])]), _vm._v(" "), _c("div", {
      staticClass: "card__face card__face--vi card__face--back"
    }, [_c("div", {
      staticClass: "card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative"
    }, [_c("div", {
      staticClass: "card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5",
      staticStyle: {
        "flex-grow": "1"
      }
    }, [_c("div", {
      staticClass: "text-center",
      domProps: {
        innerHTML: _vm._s(card.value.meaning)
      }
    })]), _vm._v(" "), _c("div", {
      staticClass: "font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]",
      domProps: {
        innerHTML: _vm._s(card.value.kanji_meaning)
      }
    }), _vm._v(" "), card.value.back_image ? _c("div", {
      staticClass: "card_img_back p-[40px] content-img text-center"
    }, [_c("img", {
      attrs: {
        src: "https://video-test.dungmori.com/images/".concat(card.value.back_image)
      }
    })]) : _vm._e(), _vm._v(" "), card.value.meaning_example.length ? _c("div", {
      staticClass: "example-wrap"
    }, [_c("p", {
      staticClass: "w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"
    }, [_vm._v("\n                      Ví dụ\n                    ")]), _vm._v(" "), _c("div", {
      staticClass: "list-example"
    }, [_vm._l(card.value.meaning_example, function (meaning_example, index) {
      return [_c("div", {
        staticClass: "example-item flex items-center mb-1"
      }, [_c("div", {
        staticClass: "ml-2 font-averta-regular text-2xl flex items-start"
      }, [_vm._v("\n                            " + _vm._s(index + 1) + ". "), _c("span", {
        staticClass: "ml-1",
        domProps: {
          innerHTML: _vm._s(meaning_example)
        }
      })])])];
    })], 2)]) : _vm._e()]), _vm._v(" "), _c("div", {
      staticClass: "how-remember-wrap px-4 h-[40%] flex flex-col"
    }, [_vm._m(16, true), _vm._v(" "), card.comment && card.comment.user_info ? _c("div", {
      staticClass: "how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"
    }, [_c("div", {
      staticClass: "how-remember-wrap-avatar w-[28px]"
    }, [_c("img", {
      staticClass: "rounded-full",
      attrs: {
        src: "/cdn/avatar/small/".concat(card.comment.user_info.avatar)
      }
    })]), _vm._v(" "), _c("div", {
      staticClass: "how-remember-wrap-info flex text-[#073A3B]"
    }, [_c("span", {
      staticClass: "font-averta-bold"
    }, [_vm._v("\n                          " + _vm._s(card.comment.user_info.name) + "・\n                        ")]), _vm._v(" "), _c("span", {
      staticClass: "font-averta-regular"
    }, [_vm._v("\n                          " + _vm._s(card.comment.time_created) + "\n                        ")]), _vm._v(" "), card.comment.pin ? _c("svg", {
      attrs: {
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }
    }, [_c("path", {
      attrs: {
        d: "M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z",
        fill: "#B3B3B3"
      }
    })]) : _vm._e()]), _vm._v(" "), _c("div", {
      staticClass: "col-start-2 font-averta-regular text-[#07403F]",
      staticStyle: {
        display: "-webkit-box",
        "-webkit-line-clamp": "2",
        "-webkit-box-orient": "vertical",
        overflow: "hidden",
        "text-overflow": "ellipsis"
      }
    }, [_vm._v("\n                      " + _vm._s(card.comment.content) + "\n                    ")]), _vm._v(" "), _c("div", {
      staticClass: "col-start-2 flex justify-between items-center"
    }, [_c("div", {
      staticClass: "font-averta-regular text-[#009951] flex"
    }, [_c("div", {
      staticClass: "flex items-center mr-5"
    }, [_c("svg", {
      staticClass: "mr-1 noFlip",
      attrs: {
        width: "13",
        height: "12",
        viewBox: "0 0 13 12",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }
    }, [_c("path", {
      attrs: {
        d: "M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z",
        fill: card.comment.comment_like.length ? "#009951" : "none",
        stroke: "#009951"
      }
    })]), _vm._v("\n                          " + _vm._s(card.comment.count_like) + "\n                        ")]), _vm._v(" "), card.comment && card.comment.replies ? _c("div", [_vm._v("\n                          " + _vm._s(card.comment.replies.length) + " Trả lời\n                        ")]) : _vm._e()]), _vm._v(" "), _c("div", {
      staticClass: "underline decoration-solid text-[#009951] cursor-pointer noFlip",
      on: {
        click: function click($event) {
          return _vm.toggleCommentTab("open");
        }
      }
    }, [_vm._v("\n                        Xem thêm >>\n                      ")])])]) : _c("div", {
      staticClass: "items-center flex grow",
      staticStyle: {
        "flex-grow": "1"
      }
    }, [_c("div", {
      staticClass: "underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip",
      on: {
        click: function click($event) {
          return _vm.toggleCommentTab("open");
        }
      }
    }, [_vm._v("\n                      Đóng góp cách nhớ của bạn >>\n                    ")])])])])])]);
  }), 0), _vm._v(" "), _c("button", {
    staticClass: "flashcard-nav-button next",
    attrs: {
      disabled: _vm.currentFlashcardIndex === _vm.favoriteFlashcards.length - 1
    },
    on: {
      click: _vm.nextFlashcard
    }
  }, [_c("i", {
    staticClass: "fas fa-chevron-right"
  })])])])]) : _vm._e()], 1);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "toggle-menu",
    attrs: {
      onclick: "toggleMenu()"
    }
  }, [_c("i", {
    staticClass: "fas fa-bars"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "nav-menu-item",
    attrs: {
      onclick: "toggleSubmenu('submenu1')"
    }
  }, [_c("span", {
    staticClass: "nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]"
  }, [_vm._v("JLPT")]), _vm._v(" "), _c("i", {
    staticClass: "fas fa-chevron-down ml-auto"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "nav-menu-item",
    attrs: {
      onclick: "toggleSubmenu('submenu2')"
    }
  }, [_c("span", {
    staticClass: "nav-menu-item-text font-beanbag-medium text-[20px] text-[#07403F]"
  }, [_vm._v("Chuyên ngành")]), _vm._v(" "), _c("i", {
    staticClass: "fas fa-chevron-down ml-auto"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "search-icon"
  }, [_c("i", {
    staticClass: "fas fa-search"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "column-bar learned-bar"
  }, [_c("div", {
    staticClass: "column-bar-inner"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "column-bar temporary-bar"
  }, [_c("div", {
    staticClass: "column-bar-inner"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "column-bar memorized-bar"
  }, [_c("div", {
    staticClass: "column-bar-inner"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "column-bar mastered-bar"
  }, [_c("div", {
    staticClass: "column-bar-inner"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "vocabulary-section-title"
  }, [_vm._v("\n              JLPT\n              "), _c("span", {
    staticClass: "badge badge-vip"
  }, [_vm._v("VIP")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "vocabulary-section-title"
  }, [_vm._v("\n              Chuyên ngành\n              "), _c("span", {
    staticClass: "badge badge-free"
  }, [_vm._v("Free")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "vocabulary-section-title"
  }, [_vm._v("\n              Yêu thích\n              "), _c("span", {
    staticClass: "badge badge-favorite"
  }, [_vm._v("Favorite")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "pl-[32px] pb-[23px]"
  }, [_c("div", {
    staticClass: "font-gen-jyuu-gothic text-[20px]"
  }, [_vm._v("Từ thêm gần đây")]), _vm._v(" "), _c("div", {
    staticClass: "favorite-word-new text-[24px]"
  }, [_vm._v("\n                        木漏れ日\n                      ")]), _vm._v(" "), _c("div", {
    staticClass: "time-add-new-word font-beanbag-regular text-[20px] text-[#757575]"
  }, [_vm._v("\n                        9:05 18-02-2025\n                      ")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "search-not-found-image"
  }, [_c("img", {
    attrs: {
      src: "/images/vocabulary/bg-search-not-found.svg",
      alt: "Không tìm thấy kết quả"
    }
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "vocabulary-section-favorite-title"
  }, [_c("div", {
    staticClass: "flex font-beanbag-medium text-[24px] text-[#07403F] items-center"
  }, [_vm._v("\n                  Từ vựng yêu thích\n                  "), _c("i", {
    staticClass: "fas fa-heart text-[#FF7C79] ml-2"
  })]), _vm._v(" "), _c("div", {
    staticClass: "font-beanbag-regular text-[20px] text-[#07403F]"
  }, [_vm._v("1122 từ")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "search-icon"
  }, [_c("i", {
    staticClass: "fas fa-search"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "flex items-center mb-10"
  }, [_c("div", {
    staticClass: "bg-[#CCF8D1] py-2 px-3 rounded-full flex font-beanbag-medium text-[20px] text-[#07403F] items-center mr-5"
  }, [_c("i", {
    staticClass: "fas fa-list mr-2"
  }), _vm._v("\n              Danh sách\n            ")]), _vm._v(" "), _c("div", {
    staticClass: "font-beanbag-regular text-[20px] text-[#B3B3B3] flex items-center"
  }, [_c("img", {
    attrs: {
      src: "/images/icons/tag-flashcard.svg",
      alt: "tag"
    }
  }), _vm._v("\n              Thẻ\n            ")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "how-remember-wrap-header flex mb-5"
  }, [_c("div", {
    staticClass: "font-beanbag-medium text-[#757575] text-xl"
  }, [_vm._v("\n                      Cách nhớ\n                    ")]), _vm._v(" "), _c("div", {
    staticClass: "border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"
  })]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=template&id=9585aae4&scoped=true":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=template&id=9585aae4&scoped=true ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* binding */ render; },
/* harmony export */   staticRenderFns: function() { return /* binding */ staticRenderFns; }
/* harmony export */ });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", [_c("div", {
    staticClass: "flashcard-wrap"
  }, [_vm.currentView === _vm.TYPES_VIEW.OVERVIEW ? _c("div", {
    staticClass: "flex flex-col justify-center items-center w-[90%] max-w-[1000px]"
  }, [_vm._m(0), _vm._v(" "), _c("div", {
    staticClass: "w-full"
  }, _vm._l(4, function (i) {
    return _c("div", {
      staticClass: "max-w-[420px] mx-auto progress-item flex justify-between items-center mb-4"
    }, [_c("div", {
      staticClass: "font-averta-regular text-[20px] text-[#1E1E1E]"
    }, [_vm._v("Đã học")]), _vm._v(" "), _c("progress-bar", {
      attrs: {
        percent: i * 20
      }
    }), _vm._v(" "), _c("div", {
      staticClass: "font-averta-regular text-[20px] text-[#757575]"
    }, [_vm._v(_vm._s(i * 20) + "%・" + _vm._s(i * 280) + " từ")])], 1);
  }), 0), _vm._v(" "), _c("div", {
    staticClass: "w-[340px]"
  }, [_c("div", {
    staticClass: "w-full text-xl text-[#07403F] font-beanbag rounded-full p-3 mt-10 flex justify-between items-center border-[1px] border-[#07403F] cursor-pointer",
    on: {
      click: function click($event) {
        _vm.currentView = _vm.TYPES_VIEW.SEARCH;
      }
    }
  }, [_c("div", {
    staticClass: "text-[#07403F] font-averta-regular text-xl"
  }, [_vm._v("\n            Danh sách toàn bộ từ\n          ")]), _vm._v(" "), _c("div", [_c("svg", {
    attrs: {
      width: "8",
      height: "8",
      viewBox: "0 0 8 8",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg"
    }
  }, [_c("path", {
    attrs: {
      d: "M0.5 4H7.5M7.5 4L4 0.5M7.5 4L4 7.5",
      stroke: "#07403F",
      "stroke-linecap": "round",
      "stroke-linejoin": "round"
    }
  })])])]), _vm._v(" "), _c("div", {
    staticClass: "text-center font-averta-regular text-xl text-[#757575] italic mt-3 text-[#EF6D13]"
  }, [_vm._v("\n          *Học thử 300 từ đầu tiên trong bộ từ vựng JLPT N5\n        ")])]), _vm._v(" "), _c("button", {
    staticClass: "w-full bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] mt-12 w-[340px] cursor-pointer"
  }, [_vm._v("\n        học ngay\n      ")])]) : _vm._e(), _vm._v(" "), _vm.currentView === _vm.TYPES_VIEW.SEARCH ? _c("div", [_c("div", {
    staticClass: "flex justify-between items-center mb-10"
  }, [_c("div", {
    staticClass: "flex items-center"
  }, [_c("div", {
    staticClass: "vocabulary-search-btn-back h-[65px] w-[65px]",
    on: {
      click: function click($event) {
        _vm.currentView = _vm.TYPES_VIEW.OVERVIEW;
      }
    }
  }, [_c("i", {
    staticClass: "fas fa-arrow-left"
  })]), _vm._v(" "), _vm._m(1)]), _vm._v(" "), _c("div", {
    staticClass: "vocabulary-favorite-search-bar flex items-center rounded-full bg-white shadow-md h-[65px] px-[20px] transition-all duration-300 flex-1 min-w-0 max-w-[400px]"
  }, [_vm._m(2), _vm._v(" "), _c("input", {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.searchQuery,
      expression: "searchQuery"
    }],
    staticClass: "search-input leading-[65px]",
    attrs: {
      type: "text",
      placeholder: "Nhập từ vựng muốn tìm kiếm"
    },
    domProps: {
      value: _vm.searchQuery
    },
    on: {
      input: function input($event) {
        if ($event.target.composing) return;
        _vm.searchQuery = $event.target.value;
      }
    }
  })])]), _vm._v(" "), _vm.searchResults.length > 0 ? _c("div", {
    staticClass: "search-result list-favorite"
  }, [_c("div", {
    staticClass: "search-result-items"
  }, _vm._l(_vm.searchResults, function (item, index) {
    return _c("div", {
      key: index,
      staticClass: "search-result-item cursor-pointer relative"
    }, [_c("div", {
      staticClass: "search-result-item-content border-b border-[#D0D3DA] border-bottom-[1px] pt-3 pb-3",
      on: {
        click: function click($event) {
          return _vm.alert(1);
        }
      }
    }, [_c("div", {
      staticClass: "search-result-item-title font-gen-jyuu-gothic-medium text-bold text-black text-[20px]"
    }, [_vm._v("\n                " + _vm._s(item.word) + "\n              ")]), _vm._v(" "), _c("div", {
      staticClass: "search-result-item-description font-gen-jyuu-gothic text-[18px] text-[#757575]"
    }, [_vm._v("\n                " + _vm._s(item.description) + "\n              ")]), _vm._v(" "), _c("div", {
      staticClass: "search-result-item-specialized w-fit font-averta-regular text-[16px] text-[#FFFFFF] bg-[#B3B3B3] rounded-full px-3 py-1"
    }, [_vm._v("\n                " + _vm._s(item.specialized) + "\n              ")])]), _vm._v(" "), item.lock ? _c("div", {
      staticClass: "absolute h-full top-0 left-0 w-full flex items-center",
      staticStyle: {
        background: "linear-gradient(to right, rgba(234, 246, 235, 1), rgba(255, 255, 255, 0))"
      }
    }, [_c("i", {
      staticClass: "fas fa-lock text-white text-4xl text-[#757575] ml-[32px]"
    })]) : _vm._e()]);
  }), 0)]) : _vm._e(), _vm._v(" "), _c("button", {
    staticClass: "fixed bottom-[56px] left-[50%] transform w-full bg-[#57D061] text-xl text-[#07403F] font-beanbag rounded-full py-3 uppercase shadow-lg shadow-[#4C5D7052] w-[340px] cursor-pointer",
    on: {
      click: function click($event) {
        _vm.currentView = _vm.TYPES_VIEW.STACKED_CARD;
      }
    }
  }, [_vm._v("\n        bắt đầu\n      ")])]) : _vm._e(), _vm._v(" "), _vm.currentView === _vm.TYPES_VIEW.STACKED_CARD ? _c("div", {
    staticClass: "flashcards-wrap"
  }, [_vm._m(3), _vm._v(" "), _c("div", {
    staticClass: "cards-wrap content mx-auto w-[80%] max-w-[648px]"
  }, [_c("div", {
    staticClass: "content-wrap mx-auto mb-6"
  }, [_c("div", {
    staticClass: "stackedcards stackedcards--animatable a_cursor--pointer",
    attrs: {
      id: "stacked-cards-block"
    }
  }, [_c("div", {
    staticClass: "stackedcards-container",
    staticStyle: {
      "margin-bottom": "20px"
    }
  }, _vm._l(_vm.dataFlashCard, function (card, index) {
    return _c("div", {
      key: card.id,
      staticClass: "card-item",
      "class": {
        "stackedcards-active": index === 0,
        "stackedcards-top": true,
        "stackedcards--animatable": true,
        "stackedcards-origin-top": true
      },
      attrs: {
        "data-id": card.id
      }
    }, [_c("div", {
      staticClass: "card-inner",
      "class": {
        flip: !_vm.isJapanese
      },
      attrs: {
        "data-id": card.id
      }
    }, [_c("div", {
      staticClass: "card__face card__face--jp card__face--front"
    }, [_c("div", {
      staticClass: "card-wrap p-4 h-[90%] overflow-y-auto relative"
    }, [_c("div", {
      staticClass: "card_header flex items-center"
    }, [card.value.audio ? _c("div", {
      staticClass: "card_audio noFlip w-[48px] h-[48px] rounded-full bg-[#E1EBFF] flex items-center justify-center mr-4 cursor-pointer",
      on: {
        click: function click($event) {
          return _vm.playAudio(card.id, card.value.audio);
        }
      }
    }, [_c("svg", {
      staticClass: "noFlip",
      attrs: {
        width: "24",
        height: "24",
        viewBox: "0 0 24 24",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }
    }, [_c("path", {
      attrs: {
        d: "M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",
        fill: "#4E87FF"
      }
    }), _vm._v(" "), _c("path", {
      attrs: {
        d: "M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",
        fill: "#4E87FF"
      }
    }), _vm._v(" "), _c("path", {
      attrs: {
        opacity: "0.4",
        d: "M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",
        fill: "#4E87FF"
      }
    }), _vm._v(" "), _c("path", {
      attrs: {
        d: "M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",
        fill: "#4E87FF"
      }
    })])]) : _vm._e(), _vm._v(" "), _c("div", {
      staticClass: "card_title font-gen-jyuu-gothic-medium text-2xl text-[#757575]",
      domProps: {
        innerHTML: _vm._s(card.value.word_stress)
      }
    })]), _vm._v(" "), _c("div", {
      staticClass: "card_content min-h-[calc(100%-34px)] flex flex-col"
    }, [_c("div", {
      staticClass: "content-text font-gen-jyuu-gothic-medium text-[56px] text-[#07403F] items-center justify-center flex",
      staticStyle: {
        "flex-grow": "1"
      },
      domProps: {
        innerHTML: _vm._s(card.value.word)
      }
    }), _vm._v(" "), card.value.front_image ? _c("div", {
      staticClass: "content-img text-center p-[40px]"
    }, [_c("img", {
      attrs: {
        src: "https://video-test.dungmori.com/images/".concat(card.value.front_image)
      }
    })]) : _vm._e(), _vm._v(" "), card.value.example.length ? _c("div", {
      staticClass: "example-wrap"
    }, [_c("p", {
      staticClass: "w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"
    }, [_vm._v("\n                            Ví dụ\n                          ")]), _vm._v(" "), _c("div", {
      staticClass: "list-example"
    }, [_vm._l(card.value.example, function (example, index) {
      return [_c("div", {
        staticClass: "example-item flex items-start mb-1"
      }, [example.audio ? _c("svg", {
        staticClass: "w-[36px] h-[36px] noFlip cursor-pointer",
        attrs: {
          width: "24",
          height: "24",
          viewBox: "0 0 24 24",
          fill: "none",
          xmlns: "http://www.w3.org/2000/svg"
        },
        on: {
          click: function click($event) {
            return _vm.playAudio(card.id + "_example_" + index, example.audio);
          }
        }
      }, [_c("path", {
        attrs: {
          d: "M18 16.7503C17.84 16.7503 17.69 16.7003 17.55 16.6003C17.22 16.3503 17.15 15.8803 17.4 15.5503C18.97 13.4603 18.97 10.5403 17.4 8.45027C17.15 8.12027 17.22 7.65027 17.55 7.40027C17.88 7.15027 18.35 7.22027 18.6 7.55027C20.56 10.1703 20.56 13.8303 18.6 16.4503C18.45 16.6503 18.23 16.7503 18 16.7503Z",
          fill: "#4E87FF"
        }
      }), _vm._v(" "), _c("path", {
        attrs: {
          d: "M19.83 19.2503C19.67 19.2503 19.52 19.2003 19.38 19.1003C19.05 18.8503 18.98 18.3803 19.23 18.0503C21.9 14.4903 21.9 9.51027 19.23 5.95027C18.98 5.62027 19.05 5.15027 19.38 4.90027C19.71 4.65027 20.18 4.72027 20.43 5.05027C23.5 9.14027 23.5 14.8603 20.43 18.9503C20.29 19.1503 20.06 19.2503 19.83 19.2503Z",
          fill: "#4E87FF"
        }
      }), _vm._v(" "), _c("path", {
        attrs: {
          opacity: "0.4",
          d: "M15.75 7.40972V16.5897C15.75 18.3097 15.13 19.5997 14.02 20.2197C13.57 20.4697 13.07 20.5897 12.55 20.5897C11.75 20.5897 10.89 20.3197 10.01 19.7697L7.09 17.9397C6.89 17.8197 6.66 17.7497 6.43 17.7497H5.5V6.24972H6.43C6.66 6.24972 6.89 6.17972 7.09 6.05972L10.01 4.22972C11.47 3.31972 12.9 3.15972 14.02 3.77972C15.13 4.39972 15.75 5.68972 15.75 7.40972Z",
          fill: "#4E87FF"
        }
      }), _vm._v(" "), _c("path", {
        attrs: {
          d: "M5.5 6.25V17.75H5C2.58 17.75 1.25 16.42 1.25 14V10C1.25 7.58 2.58 6.25 5 6.25H5.5Z",
          fill: "#4E87FF"
        }
      })]) : _vm._e(), _vm._v(" "), _c("div", {
        staticClass: "ml-2 font-beanbag-regular text-2xl flex items-start w-[90%]"
      }, [_vm._v("\n                                  " + _vm._s(index + 1) + ". "), _c("span", {
        staticClass: "ml-2",
        domProps: {
          innerHTML: _vm._s(example.example)
        }
      })])])];
    })], 2)]) : _vm._e()])]), _vm._v(" "), _c("div", {
      staticClass: "card-footer noFlip flex justify-end border-t-[1px] border-[#D0D3DA]"
    }, [_c("svg", {
      staticClass: "m-3 noFlip",
      attrs: {
        width: "33",
        height: "28",
        viewBox: "0 0 33 28",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }
    }, [_c("path", {
      attrs: {
        d: "M23.8333 0.5C28.6377 0.5 32.4997 4.21737 32.5 8.78727C32.4819 14.1271 29.8923 18.3826 26.6876 21.5156C23.4787 24.6525 19.6885 26.627 17.3726 27.393C17.163 27.4593 16.8527 27.5 16.5137 27.5C16.1782 27.5 15.8562 27.4601 15.6229 27.3915C13.3062 26.6243 9.50962 24.6502 6.29845 21.5153C3.08901 18.3822 0.5 14.127 0.5 8.78779C0.5 4.21765 4.36212 0.5 9.16667 0.5C12.0183 0.5 14.5145 1.79375 16.1076 3.81099L16.5 4.30783L16.8924 3.81099C18.4855 1.79375 20.9817 0.5 23.8333 0.5Z",
        fill: "#FF7C79",
        stroke: "#FF7C79"
      }
    })])])]), _vm._v(" "), _c("div", {
      staticClass: "card__face card__face--vi card__face--back"
    }, [_c("div", {
      staticClass: "card-wrap-back p-4 h-[60%] overflow-y-auto flex flex-col relative"
    }, [_c("div", {
      staticClass: "card_content_back font-beanbag-medium text-5xl items-center justify-center flex text-[#07403F] mb-5",
      staticStyle: {
        "flex-grow": "1"
      }
    }, [_c("div", {
      staticClass: "text-center",
      domProps: {
        innerHTML: _vm._s(card.value.meaning)
      }
    })]), _vm._v(" "), _c("div", {
      staticClass: "font-beanbag-medium text-[#757575] text-xl text-center text-[#07403F]",
      domProps: {
        innerHTML: _vm._s(card.value.kanji_meaning)
      }
    }), _vm._v(" "), card.value.back_image ? _c("div", {
      staticClass: "card_img_back p-[40px] content-img text-center"
    }, [_c("img", {
      attrs: {
        src: "https://video-test.dungmori.com/images/".concat(card.value.back_image)
      }
    })]) : _vm._e(), _vm._v(" "), card.value.meaning_example.length ? _c("div", {
      staticClass: "example-wrap"
    }, [_c("p", {
      staticClass: "w-fit font-beanbag text-base text-[#07403F] bg-[#CEFFD8] rounded-[10px] p-2 mb-2"
    }, [_vm._v("\n                          Ví dụ\n                        ")]), _vm._v(" "), _c("div", {
      staticClass: "list-example"
    }, [_vm._l(card.value.meaning_example, function (meaning_example, index) {
      return [_c("div", {
        staticClass: "example-item flex items-center mb-1"
      }, [_c("div", {
        staticClass: "ml-2 font-averta-regular text-2xl flex items-start"
      }, [_vm._v("\n                                " + _vm._s(index + 1) + ". "), _c("span", {
        staticClass: "ml-1",
        domProps: {
          innerHTML: _vm._s(meaning_example)
        }
      })])])];
    })], 2)]) : _vm._e()]), _vm._v(" "), _c("div", {
      staticClass: "how-remember-wrap px-4 h-[40%] flex flex-col"
    }, [_vm._m(4, true), _vm._v(" "), card.comment && card.comment.user_info ? _c("div", {
      staticClass: "how-remember-wrap-content grid grid-cols-[40px_auto] gap-4"
    }, [_c("div", {
      staticClass: "how-remember-wrap-avatar w-[28px]"
    }, [_c("img", {
      staticClass: "rounded-full",
      attrs: {
        src: "/cdn/avatar/small/".concat(card.comment.user_info.avatar)
      }
    })]), _vm._v(" "), _c("div", {
      staticClass: "how-remember-wrap-info flex text-[#073A3B]"
    }, [_c("span", {
      staticClass: "font-averta-bold"
    }, [_vm._v("\n                          " + _vm._s(card.comment.user_info.name) + "・\n                        ")]), _vm._v(" "), _c("span", {
      staticClass: "font-averta-regular"
    }, [_vm._v("\n                          " + _vm._s(card.comment.time_created) + "\n                        ")]), _vm._v(" "), card.comment.pin ? _c("svg", {
      attrs: {
        width: "16",
        height: "16",
        viewBox: "0 0 16 16",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }
    }, [_c("path", {
      attrs: {
        d: "M4.54059 9.86604C4.49626 9.83523 4.45013 9.81231 4.41581 9.77792C3.83372 9.198 3.25271 8.617 2.67312 8.03457C2.59482 7.95577 2.51759 7.87338 2.45323 7.78312C2.23084 7.47256 2.23799 7.15555 2.48076 6.86004C2.75322 6.52871 3.11005 6.30626 3.49405 6.13218C4.1902 5.81697 4.90958 5.70163 5.65114 5.95953C5.851 6.02902 6.03657 6.14078 6.21856 6.22854C6.924 5.52396 7.63981 4.80935 8.36242 4.08758C8.13788 3.75446 8.06279 3.37477 8.17649 2.96821C8.44966 1.99177 9.64601 1.67476 10.3836 2.38543C10.7887 2.77586 11.1827 3.17776 11.5803 3.57608C12.2554 4.25271 12.9347 4.92469 13.6019 5.60885C14.4236 6.45133 13.8447 7.6316 13.0213 7.84616C12.6641 7.93929 12.3312 7.88628 12.0212 7.69106C11.9354 7.63697 11.8721 7.63733 11.7981 7.71148C11.1216 8.39277 10.4426 9.07191 9.76472 9.75177C9.75864 9.75786 9.75542 9.76682 9.74648 9.78186C9.75363 9.79691 9.76078 9.81732 9.77223 9.83523C10.3089 10.6745 10.3089 11.5489 9.90023 12.4272C9.74755 12.7553 9.5248 13.0526 9.31814 13.3527C9.24735 13.4555 9.13579 13.5365 9.0296 13.6074C8.81114 13.7532 8.57373 13.7629 8.34561 13.635C8.22297 13.5662 8.10677 13.4767 8.00701 13.3775C7.36128 12.7352 6.71877 12.089 6.08019 11.4392C5.99331 11.3508 5.94289 11.3429 5.84278 11.4246C4.85559 12.2341 3.86411 13.0382 2.87299 13.8431C2.60555 14.0605 2.32702 14.0509 2.12286 13.822C1.99378 13.6773 1.96304 13.515 2.04277 13.3384C2.07745 13.2618 2.12286 13.1887 2.17256 13.1206C2.9209 12.0915 3.67104 11.0635 4.42046 10.0351C4.45407 9.9889 4.4866 9.94198 4.53988 9.8664L4.54059 9.86604Z",
        fill: "#B3B3B3"
      }
    })]) : _vm._e()]), _vm._v(" "), _c("div", {
      staticClass: "col-start-2 font-averta-regular text-[#07403F]",
      staticStyle: {
        display: "-webkit-box",
        "-webkit-line-clamp": "2",
        "-webkit-box-orient": "vertical",
        overflow: "hidden",
        "text-overflow": "ellipsis"
      }
    }, [_vm._v("\n                          " + _vm._s(card.comment.content) + "\n                        ")]), _vm._v(" "), _c("div", {
      staticClass: "col-start-2 flex justify-between items-center"
    }, [_c("div", {
      staticClass: "font-averta-regular text-[#009951] flex"
    }, [_c("div", {
      staticClass: "flex items-center mr-5"
    }, [_c("svg", {
      staticClass: "mr-1 noFlip",
      attrs: {
        width: "13",
        height: "12",
        viewBox: "0 0 13 12",
        fill: "none",
        xmlns: "http://www.w3.org/2000/svg"
      }
    }, [_c("path", {
      attrs: {
        d: "M9.38889 0.723633C11.1046 0.723633 12.4996 2.1159 12.5 3.84852C12.493 5.95749 11.518 7.64456 10.2933 8.89844C9.06415 10.1569 7.61344 10.9461 6.74434 11.2477C6.70298 11.2611 6.61724 11.2756 6.50542 11.2756C6.39725 11.2756 6.30516 11.262 6.25054 11.2459C5.38046 10.9429 3.92943 10.1541 2.70106 8.89818C1.47455 7.64419 0.5 5.95749 0.5 3.8492C0.5 2.11626 1.89522 0.723633 3.61111 0.723633C4.6312 0.723633 5.52625 1.20724 6.10079 1.96912L6.5 2.49851L6.89921 1.96912C7.47375 1.20724 8.3688 0.723633 9.38889 0.723633Z",
        fill: card.comment.comment_like.length ? "#009951" : "none",
        stroke: "#009951"
      }
    })]), _vm._v("\n                              " + _vm._s(card.comment.count_like) + "\n                            ")]), _vm._v(" "), card.comment && card.comment.replies ? _c("div", [_vm._v("\n                              " + _vm._s(card.comment.replies.length) + " Trả lời\n                            ")]) : _vm._e()]), _vm._v(" "), _c("div", {
      staticClass: "underline decoration-solid text-[#009951] cursor-pointer noFlip",
      on: {
        click: function click($event) {
          return _vm.toggleCommentTab("open");
        }
      }
    }, [_vm._v("\n                            Xem thêm >>\n                          ")])])]) : _c("div", {
      staticClass: "items-center flex grow",
      staticStyle: {
        "flex-grow": "1"
      }
    }, [_c("div", {
      staticClass: "underline decoration-solid font-averta-regular text-[#009951] cursor-pointer noFlip",
      on: {
        click: function click($event) {
          return _vm.toggleCommentTab("open");
        }
      }
    }, [_vm._v("\n                          Đóng góp cách nhớ của bạn >>\n                        ")])])])])])]);
  }), 0), _vm._v(" "), _c("div", {
    staticClass: "stackedcards--animatable stackedcards-overlay left stackedcards-origin-top font-averta-bold text-[#975102]"
  }, [_vm._v("\n              Chưa nhớ\n            ")]), _vm._v(" "), _c("div", {
    staticClass: "stackedcards--animatable stackedcards-overlay right stackedcards-origin-top font-averta-bold text-[#02542D]"
  }, [_vm._v("\n              Đã học\n            ")])])])])]) : _vm._e()])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "w-full text-center p-4 rounded-3xl mb-6",
    staticStyle: {
      background: "linear-gradient(to bottom, #FFFFFF, #F4F5FA)"
    }
  }, [_c("div", {
    staticClass: "img mb-7"
  }, [_c("img", {
    attrs: {
      src: "/images/vocabulary/img-over-view-course.svg",
      alt: ""
    }
  })]), _vm._v(" "), _c("div", {
    staticClass: "title text-center text-[#07403F]"
  }, [_c("div", {
    staticClass: "font-beanbag-medium text-[20px] uppercase"
  }, [_vm._v("Bộ từ vựng")]), _vm._v(" "), _c("div", {
    staticClass: "font-zuume-semibold text-[48px] text-bold uppercase"
  }, [_vm._v("jlpt n5")]), _vm._v(" "), _c("div", {
    staticClass: "font-averta-regular text-[20px]"
  }, [_vm._v("Học và ghi nhớ 1122 từ vựng thuộc cấp độ N5")])])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "vocabulary-section-favorite-title"
  }, [_c("div", {
    staticClass: "flex font-beanbag-medium text-[24px] text-[#07403F] items-center"
  }, [_vm._v("\n              Từ vựng yêu thích\n              "), _c("i", {
    staticClass: "fas fa-heart text-[#FF7C79] ml-2"
  })]), _vm._v(" "), _c("div", {
    staticClass: "font-beanbag-regular text-[20px] text-[#07403F]"
  }, [_vm._v("1122 từ")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "search-icon"
  }, [_c("i", {
    staticClass: "fas fa-search"
  })]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "mx-auto w-[80%] max-w-[648px]"
  }, [_c("div", {
    staticClass: "tag_card rounded-full px-2 py-1 bg-[#14AE5C] font-beanbag-medium text-white text-base flex items-center mb-4",
    staticStyle: {
      "line-height": "1",
      width: "fit-content",
      "align-self": "flex-start"
    }
  }, [_vm._v("\n          Từ vựng ôn lại\n        ")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "how-remember-wrap-header flex mb-5"
  }, [_c("div", {
    staticClass: "font-beanbag-medium text-[#757575] text-xl"
  }, [_vm._v("\n                          Cách nhớ\n                        ")]), _vm._v(" "), _c("div", {
    staticClass: "border-b-[1px] border-[#D0D3DA] flex-1 m-[7px]"
  })]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/is-buffer/index.js":
/*!*****************************************!*\
  !*** ./node_modules/is-buffer/index.js ***!
  \*****************************************/
/***/ (function(module) {

/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */

// The _isBuffer check is for Safari 5-7 support, because it's missing
// Object.prototype.constructor. Remove this eventually
module.exports = function (obj) {
  return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer)
}

function isBuffer (obj) {
  return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)
}

// For Node v0.10 support. Remove this eventually.
function isSlowBuffer (obj) {
  return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isBuffer(obj.slice(0, 0))
}


/***/ }),

/***/ "./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js */ "./node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);
// Imports

var ___CSS_LOADER_EXPORT___ = _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.id, "\n.progress-bar[data-v-1c837bb2] {\n  -webkit-box-flex: 1;\n  -webkit-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  margin: 0 15px;\n  position: relative;\n  border-radius: 9999px;\n  overflow: hidden;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n}\n.progress-bar-inner[data-v-1c837bb2] {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 100%;\n  border-radius: 9999px;\n  -webkit-transition: width 0.6s ease;\n  transition: width 0.6s ease;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n}\n", ""]);
// Exports
/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js */ "./node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);
// Imports

var ___CSS_LOADER_EXPORT___ = _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.id, "\n.flashcard-module[data-v-5dd576c5] {\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n}\n.card-inner[data-v-5dd576c5] {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  text-align: center;\n  -webkit-transition: -webkit-transform 0.6s;\n  transition: -webkit-transform 0.6s;\n  transition: transform 0.6s;\n  transition: transform 0.6s, -webkit-transform 0.6s;\n  -webkit-transform-style: preserve-3d;\n          transform-style: preserve-3d;\n}\n.card-inner.flip[data-v-5dd576c5] {\n  -webkit-transform: rotateY(180deg);\n          transform: rotateY(180deg);\n}\n.card__face[data-v-5dd576c5] {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  -webkit-backface-visibility: hidden;\n  backface-visibility: hidden;\n  border-radius: 20px;\n  overflow: hidden;\n}\n.card__face--front[data-v-5dd576c5] {\n  background-color: #ffffff;\n}\n.card__face--back[data-v-5dd576c5] {\n  background-color: #ffffff;\n  -webkit-transform: rotateY(180deg);\n          transform: rotateY(180deg);\n}\n.action-button[data-v-5dd576c5] {\n  cursor: pointer;\n  -webkit-transition: -webkit-transform 0.2s;\n  transition: -webkit-transform 0.2s;\n  transition: transform 0.2s;\n  transition: transform 0.2s, -webkit-transform 0.2s;\n}\n.action-button[data-v-5dd576c5]:hover {\n  -webkit-transform: scale(1.1);\n      -ms-transform: scale(1.1);\n          transform: scale(1.1);\n}\n\n/* Stacked cards styling */\n.stackedcards-container[data-v-5dd576c5] {\n  position: relative;\n  height: 500px;\n  width: 100%;\n  max-width: 600px;\n  margin: 0 auto;\n}\n.card-item[data-v-5dd576c5] {\n  position: absolute;\n  height: 500px;\n  width: 100%;\n  left: 0;\n  right: 0;\n  margin: 0 auto;\n  -webkit-box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);\n          box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);\n  border-radius: 20px;\n  background: white;\n  -webkit-transition: opacity 0.3s, -webkit-transform 0.3s ease;\n  transition: opacity 0.3s, -webkit-transform 0.3s ease;\n  transition: transform 0.3s ease, opacity 0.3s;\n  transition: transform 0.3s ease, opacity 0.3s, -webkit-transform 0.3s ease;\n}\n.stackedcards-overlay[data-v-5dd576c5] {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0;\n  pointer-events: none;\n  -webkit-transition: opacity 0.3s;\n  transition: opacity 0.3s;\n}\n.stackedcards-overlay.left[data-v-5dd576c5] {\n  background: rgba(255, 124, 121, 0.2);\n  border-radius: 20px;\n}\n.stackedcards-overlay.right[data-v-5dd576c5] {\n  background: rgba(87, 208, 97, 0.2);\n  border-radius: 20px;\n}\n", ""]);
// Exports
/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js */ "./node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);
// Imports

var ___CSS_LOADER_EXPORT___ = _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.id, "\n/* Vocabulary Search Bar */\n.vocabulary-search-container[data-v-5f98c1fb] {\n  width: 100%;\n  margin: 0 auto 30px;\n  position: relative;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-transition: all 0.3s ease-in-out;\n  transition: all 0.3s ease-in-out; /* Animation cho container */\n}\n.vocabulary-search-bar[data-v-5f98c1fb]:hover, .vocabulary-favorite-search-bar[data-v-5f98c1fb]:hover {\n  -webkit-box-shadow: 0 1px 6px rgba(32, 33, 36, 0.4);\n          box-shadow: 0 1px 6px rgba(32, 33, 36, 0.4);\n}\n.vocabulary-search-bar[data-v-5f98c1fb]:focus-within, .vocabulary-favorite-search-bar[data-v-5f98c1fb]:focus-within {\n  -webkit-box-shadow: 0 1px 10px rgba(32, 33, 36, 0.4);\n          box-shadow: 0 1px 10px rgba(32, 33, 36, 0.4);\n}\n\n/* Style cho button quay lại */\n.vocabulary-search-btn-back[data-v-5f98c1fb] {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  background-color: #fff;\n  border-radius: 50%;\n  -webkit-box-shadow: 0 1px 6px rgba(32, 33, 36, 0.28);\n          box-shadow: 0 1px 6px rgba(32, 33, 36, 0.28);\n  margin-right: 15px;\n  -webkit-transition: all 0.2s;\n  transition: all 0.2s;\n  cursor: pointer;\n}\n.vocabulary-search-btn-back[data-v-5f98c1fb]:hover {\n  -webkit-box-shadow: 0 1px 6px rgba(32, 33, 36, 0.4);\n          box-shadow: 0 1px 6px rgba(32, 33, 36, 0.4);\n  background-color: rgba(7, 64, 63, 0.05);\n}\n.vocabulary-search-btn-back i[data-v-5f98c1fb] {\n  font-size: 22px;\n  color: #07403F;\n}\n.search-icon[data-v-5f98c1fb] {\n  color: #9aa0a6;\n  margin-right: 12px;\n  font-size: 22px;\n}\n.search-input[data-v-5f98c1fb] {\n  -webkit-box-flex: 1;\n  -webkit-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  border: none;\n  outline: none;\n  font-size: 16px;\n  color: #202124;\n  background: transparent;\n  width: 100%; /* Đảm bảo input lấp đầy không gian */\n  -webkit-transition: all 0.3s ease-in-out;\n  transition: all 0.3s ease-in-out; /* Animation mượt mà khi thay đổi kích thước */\n}\n.search-input[data-v-5f98c1fb]::-webkit-input-placeholder {\n  color: #9aa0a6;\n  font-family: \"Averta-Regular\";\n  font-size: 20px;\n}\n.search-input[data-v-5f98c1fb]::-moz-placeholder {\n  color: #9aa0a6;\n  font-family: \"Averta-Regular\";\n  font-size: 20px;\n}\n.search-input[data-v-5f98c1fb]:-ms-input-placeholder {\n  color: #9aa0a6;\n  font-family: \"Averta-Regular\";\n  font-size: 20px;\n}\n.search-input[data-v-5f98c1fb]::-ms-input-placeholder {\n  color: #9aa0a6;\n  font-family: \"Averta-Regular\";\n  font-size: 20px;\n}\n.search-input[data-v-5f98c1fb]::placeholder {\n  color: #9aa0a6;\n  font-family: \"Averta-Regular\";\n  font-size: 20px;\n}\n\n/* Chart Overview Styles */\n.chart-overview-container[data-v-5f98c1fb] {\n  height: 430px;\n}\n.chart-header[data-v-5f98c1fb] {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-flex-direction: column;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  margin-left: 12px;\n  background: rgba(240, 255, 241, 1);\n  border-radius: 12px;\n  padding: 20px;\n  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n.checkbox-container[data-v-5f98c1fb] {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  cursor: pointer;\n}\n\n/* Ẩn checkbox mặc định */\n.checkbox-container input[type=\"checkbox\"][data-v-5f98c1fb] {\n  position: absolute;\n  opacity: 0;\n  cursor: pointer;\n  height: 0;\n  width: 0;\n}\n\n/* Cập nhật style cho checkbox container */\n.filter-checkboxes .checkbox-container[data-v-5f98c1fb] {\n  position: relative;\n  padding-left: 28px;\n  margin-bottom: 8px;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  cursor: pointer;\n}\n\n/* Tạo checkbox giả hình tròn */\n.filter-checkboxes .checkbox-container[data-v-5f98c1fb]::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  -webkit-transform: translateY(-50%);\n      -ms-transform: translateY(-50%);\n          transform: translateY(-50%);\n  width: 18px;\n  height: 18px;\n  border: 2px solid #407845;\n  border-radius: 50%; /* Làm tròn checkbox */\n  background-color: #fff;\n  -webkit-transition: all 0.2s ease;\n  transition: all 0.2s ease;\n}\n\n/* Tạo dấu check bên trong */\n.filter-checkboxes .checkbox-container input[type=\"checkbox\"]:checked ~ .checkbox-label[data-v-5f98c1fb]::after {\n  content: '';\n  position: absolute;\n  left: -24px;\n  top: 50%;\n  -webkit-transform: translateY(-50%);\n      -ms-transform: translateY(-50%);\n          transform: translateY(-50%);\n  width: 10px;\n  height: 10px;\n  border-radius: 50%;\n  background-color: #407845;\n  -webkit-transition: all 0.2s ease;\n  transition: all 0.2s ease;\n}\n\n/* Hiệu ứng hover */\n.filter-checkboxes .checkbox-container[data-v-5f98c1fb]:hover::before {\n  border-color: #2c5530;\n}\n.filter-checkboxes .checkbox-label[data-v-5f98c1fb] {\n  color: #176867;\n  position: relative;\n  cursor: pointer;\n}\n.chart-container[data-v-5f98c1fb] {\n  height: 100%;\n  position: relative;\n  background-color: #fff;\n  border-radius: 12px;\n  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  padding: 30px;\n}\n\n/* HTML/CSS Chart Styles */\n.html-chart[data-v-5f98c1fb] {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  background-repeat: no-repeat;\n  background-position: top center;\n  background-size: auto 150px;\n  background-color: transparent;\n  padding: 20px;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: end;\n  -webkit-align-items: flex-end;\n      -ms-flex-align: end;\n          align-items: flex-end;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  padding-bottom: 50px;\n}\n.chart-columns[data-v-5f98c1fb] {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-justify-content: space-around;\n      -ms-flex-pack: distribute;\n          justify-content: space-around;\n  -webkit-box-align: end;\n  -webkit-align-items: flex-end;\n      -ms-flex-align: end;\n          align-items: flex-end;\n  width: 100%;\n  height: 100%;\n  border-bottom: 2px solid #176867;\n  background-image: url('/images/vocabulary/bg-chart-overview.svg');\n  background-repeat: no-repeat;\n  background-position: bottom;\n  background-size: contain;\n}\n.chart-column[data-v-5f98c1fb] {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-flex-direction: column;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  width: 20%;\n  max-width: 75px;\n  position: relative;\n  -webkit-transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);\n  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);\n}\n.column-value[data-v-5f98c1fb] {\n  font-size: 18px;\n  font-weight: bold;\n  color: rgba(31, 114, 116, 1);\n  margin-bottom: 5px;\n  position: absolute;\n  top: -40px;\n  width: 100%;\n  text-align: center;\n  fill: rgba(255, 255, 255, 0.60);\n  -webkit-filter: drop-shadow(0px 5.518px 5.518px rgba(97, 124, 154, 0.15));\n          filter: drop-shadow(0px 5.518px 5.518px rgba(97, 124, 154, 0.15));\n  -webkit-backdrop-filter: blur(3.6785595417022705px);\n          backdrop-filter: blur(3.6785595417022705px);\n  background: white;\n  border-radius: 8px;\n}\n.column-value[data-v-5f98c1fb]::after {\n  content: \"\";\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border-width: 5px;\n  border-style: solid;\n  border-color: white transparent transparent transparent;\n}\n.column-bar[data-v-5f98c1fb] {\n  height: 100%;\n  width: 100%;\n  border-radius: 8px 8px 0 0;\n  position: relative;\n  overflow: hidden;\n  -webkit-transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);\n  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);\n  -webkit-transform-origin: bottom center;\n      -ms-transform-origin: bottom center;\n          transform-origin: bottom center;\n  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\n          box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\n}\n.learned-bar[data-v-5f98c1fb] {\n  background-color: rgba(255, 106, 0, 0.31);\n}\n.temporary-bar[data-v-5f98c1fb] {\n  background-color: rgba(255, 217, 0, 0.42);\n}\n.memorized-bar[data-v-5f98c1fb] {\n  background-color: rgba(128, 255, 0, 0.37);\n}\n.mastered-bar[data-v-5f98c1fb] {\n  background: url(\"/images/vocabulary/bg-mastered-bar-column-chart-overview.svg\"), -webkit-linear-gradient(58deg, rgba(0, 255, 115, 0.50) 32.16%, rgba(0, 255, 84, 0.50) 50.05%, rgba(0, 255, 22, 0.50) 71.77%, rgba(255, 255, 0, 0.50) 96.05%);\n  background: url(\"/images/vocabulary/bg-mastered-bar-column-chart-overview.svg\"), linear-gradient(32deg, rgba(0, 255, 115, 0.50) 32.16%, rgba(0, 255, 84, 0.50) 50.05%, rgba(0, 255, 22, 0.50) 71.77%, rgba(255, 255, 0, 0.50) 96.05%);\n  background-position: top center;\n  background-repeat: no-repeat;\n}\n.column-label[data-v-5f98c1fb] {\n  font-size: 22px;\n  color: rgba(7, 64, 63, 1);\n  text-align: center;\n  position: absolute;\n  bottom: -35px;\n  width: -webkit-max-content;\n  width: -moz-max-content;\n  width: max-content;\n  font-family: \"Beanbag_Dungmori_Rounded_Medium\";\n}\n@media (max-width: 768px) {\n.chart-header[data-v-5f98c1fb] {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -webkit-flex-direction: column;\n        -ms-flex-direction: column;\n            flex-direction: column;\n}\n.chart-filters[data-v-5f98c1fb] {\n    margin-top: 10px;\n}\n.filter-checkboxes[data-v-5f98c1fb] {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n    -webkit-flex-direction: column;\n        -ms-flex-direction: column;\n            flex-direction: column;\n    gap: 8px;\n}\n}\n\n/* Style cho thanh scrollbar */\n.filter-checkboxes[data-v-5f98c1fb]::-webkit-scrollbar {\n  width: 6px; /* Độ rộng thanh scrollbar */\n}\n.filter-checkboxes[data-v-5f98c1fb]::-webkit-scrollbar-track {\n  background: transparent; /* Không có background */\n}\n.filter-checkboxes[data-v-5f98c1fb]::-webkit-scrollbar-thumb {\n  background-color: #407845; /* Màu của thanh scrollbar */\n  border-radius: 10px; /* Bo tròn thanh scrollbar */\n  border: none; /* Không có border */\n}\n\n/* Ẩn các nút ở 2 đầu trên dưới */\n.filter-checkboxes[data-v-5f98c1fb]::-webkit-scrollbar-button {\n  display: none; /* Ẩn các nút ở 2 đầu */\n}\n\n/* Style cho Firefox */\n.filter-checkboxes[data-v-5f98c1fb] {\n  scrollbar-width: thin; /* Độ rộng thanh scrollbar trên Firefox */\n  scrollbar-color: #407845 transparent; /* Màu của thanh scrollbar và background trên Firefox */\n}\n.dialog-wrapper.custom-scrollbar[data-v-5f98c1fb] {\n  overflow-y: auto;\n  overflow-x: hidden;\n  scrollbar-width: thin;\n  scrollbar-color: #57D061 transparent;\n}\n.custom-scrollbar[data-v-5f98c1fb]::-webkit-scrollbar {\n  width: 6px;\n}\n.custom-scrollbar[data-v-5f98c1fb]::-webkit-scrollbar-track {\n  background: transparent;\n}\n.custom-scrollbar[data-v-5f98c1fb]::-webkit-scrollbar-thumb {\n  background-color: #407845;\n  border-radius: 10px;\n  border: none;\n}\n\n/* Style cho phần không tìm thấy kết quả */\n.search-not-found[data-v-5f98c1fb] {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-flex-direction: column;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  padding: 40px 20px;\n  text-align: center;\n  min-height: 400px;\n}\n.search-not-found-image[data-v-5f98c1fb] {\n  margin-bottom: 20px;\n  max-width: 250px;\n  width: 100%;\n}\n.search-not-found-image img[data-v-5f98c1fb] {\n  width: 100%;\n  height: auto;\n}\n.search-not-found-text[data-v-5f98c1fb] {\n  font-size: 18px;\n  color: #757575;\n  font-family: \"Averta-Regular\";\n  max-width: 400px;\n  line-height: 1.5;\n}\n", ""]);
// Exports
/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js */ "./node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js");
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);
// Imports

var ___CSS_LOADER_EXPORT___ = _node_modules_laravel_mix_node_modules_css_loader_dist_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.id, "\n.dialog-vocabulary-info .el-dialog {\n  background: url(\"/images/vocabulary/bg-info-popup.svg\"), #FFFFFF !important;\n  background-position: top center !important;\n  background-repeat: no-repeat !important;\n  border-radius: 32px; /* Bo viền */\n  max-width: 1224px !important;\n}\n\n/* Flashcard Popup Styles */\n.flashcard-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.7);\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  z-index: 1000;\n}\n.flashcard-popup {\n  width: 90%;\n  max-width: 800px;\n  max-height: 90vh;\n}\n.flashcard-popup-content {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: justify;\n  -webkit-justify-content: space-between;\n      -ms-flex-pack: justify;\n          justify-content: space-between;\n  padding: 30px;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n}\n.flashcard-nav-button {\n  background-color: #CCF8D1;\n  color: #07403F;\n  border: none;\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  font-size: 18px;\n  cursor: pointer;\n  -webkit-transition: background-color 0.2s, -webkit-transform 0.2s;\n  transition: background-color 0.2s, -webkit-transform 0.2s;\n  transition: background-color 0.2s, transform 0.2s;\n  transition: background-color 0.2s, transform 0.2s, -webkit-transform 0.2s;\n}\n.flashcard-nav-button:hover {\n  background-color: #0a5e5c;\n  -webkit-transform: scale(1.05);\n      -ms-transform: scale(1.05);\n          transform: scale(1.05);\n}\n.flashcard-nav-button:disabled {\n  background-color: #ccc;\n  cursor: not-allowed;\n  -webkit-transform: none;\n      -ms-transform: none;\n          transform: none;\n}\n.flashcard-container {\n  -webkit-box-flex: 1;\n  -webkit-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-perspective: 1000px;\n          perspective: 1000px;\n  margin: 0 20px;\n  cursor: pointer;\n}\n.flashcard {\n  width: 100%;\n  height: 400px;\n  position: relative;\n  -webkit-transform-style: preserve-3d;\n          transform-style: preserve-3d;\n  -webkit-transition: -webkit-transform 0.6s;\n  transition: -webkit-transform 0.6s;\n  transition: transform 0.6s;\n  transition: transform 0.6s, -webkit-transform 0.6s;\n}\n.flashcard.flipped {\n  -webkit-transform: rotateY(180deg);\n          transform: rotateY(180deg);\n}\n.flashcard-front, .flashcard-back {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  -webkit-backface-visibility: hidden;\n          backface-visibility: hidden;\n  border-radius: 16px;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-flex-direction: column;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n      -ms-flex-align: center;\n          align-items: center;\n  padding: 30px;\n  -webkit-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n}\n.flashcard-front {\n  background-color: #f8f9fa;\n  border: 2px solid #07403F;\n}\n.flashcard-back {\n  background-color: #07403F;\n  color: white;\n  -webkit-transform: rotateY(180deg);\n          transform: rotateY(180deg);\n}\n.flashcard-word {\n  font-size: 48px;\n  font-weight: bold;\n  margin-bottom: 20px;\n  text-align: center;\n}\n.flashcard-reading {\n  font-size: 24px;\n  color: #666;\n  text-align: center;\n}\n.flashcard-meaning {\n  font-size: 36px;\n  font-weight: bold;\n  margin-bottom: 30px;\n  text-align: center;\n}\n.flashcard-example {\n  width: 100%;\n  max-width: 500px;\n}\n.example-jp {\n  font-size: 20px;\n  margin-bottom: 10px;\n  text-align: center;\n}\n.example-vi {\n  font-size: 18px;\n  color: #ccc;\n  text-align: center;\n}\n", ""]);
// Exports
/* harmony default export */ __webpack_exports__["default"] = (___CSS_LOADER_EXPORT___);


/***/ }),

/***/ "./node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js":
/*!******************************************************************************!*\
  !*** ./node_modules/laravel-mix/node_modules/css-loader/dist/runtime/api.js ***!
  \******************************************************************************/
/***/ (function(module) {

"use strict";


/*
  MIT License http://www.opensource.org/licenses/mit-license.php
  Author Tobias Koppers @sokra
*/
// css base code, injected by the css-loader
// eslint-disable-next-line func-names
module.exports = function (cssWithMappingToString) {
  var list = []; // return the list of modules as css string

  list.toString = function toString() {
    return this.map(function (item) {
      var content = cssWithMappingToString(item);

      if (item[2]) {
        return "@media ".concat(item[2], " {").concat(content, "}");
      }

      return content;
    }).join("");
  }; // import a list of modules into the list
  // eslint-disable-next-line func-names


  list.i = function (modules, mediaQuery, dedupe) {
    if (typeof modules === "string") {
      // eslint-disable-next-line no-param-reassign
      modules = [[null, modules, ""]];
    }

    var alreadyImportedModules = {};

    if (dedupe) {
      for (var i = 0; i < this.length; i++) {
        // eslint-disable-next-line prefer-destructuring
        var id = this[i][0];

        if (id != null) {
          alreadyImportedModules[id] = true;
        }
      }
    }

    for (var _i = 0; _i < modules.length; _i++) {
      var item = [].concat(modules[_i]);

      if (dedupe && alreadyImportedModules[item[0]]) {
        // eslint-disable-next-line no-continue
        continue;
      }

      if (mediaQuery) {
        if (!item[2]) {
          item[2] = mediaQuery;
        } else {
          item[2] = "".concat(mediaQuery, " and ").concat(item[2]);
        }
      }

      list.push(item);
    }
  };

  return list;
};

/***/ }),

/***/ "./node_modules/process/browser.js":
/*!*****************************************!*\
  !*** ./node_modules/process/browser.js ***!
  \*****************************************/
/***/ (function(module) {

// shim for using process in browser
var process = module.exports = {};

// cached from whatever global is present so that test runners that stub it
// don't break things.  But we need to wrap it in a try catch in case it is
// wrapped in strict mode code which doesn't define any globals.  It's inside a
// function because try/catches deoptimize in certain engines.

var cachedSetTimeout;
var cachedClearTimeout;

function defaultSetTimout() {
    throw new Error('setTimeout has not been defined');
}
function defaultClearTimeout () {
    throw new Error('clearTimeout has not been defined');
}
(function () {
    try {
        if (typeof setTimeout === 'function') {
            cachedSetTimeout = setTimeout;
        } else {
            cachedSetTimeout = defaultSetTimout;
        }
    } catch (e) {
        cachedSetTimeout = defaultSetTimout;
    }
    try {
        if (typeof clearTimeout === 'function') {
            cachedClearTimeout = clearTimeout;
        } else {
            cachedClearTimeout = defaultClearTimeout;
        }
    } catch (e) {
        cachedClearTimeout = defaultClearTimeout;
    }
} ())
function runTimeout(fun) {
    if (cachedSetTimeout === setTimeout) {
        //normal enviroments in sane situations
        return setTimeout(fun, 0);
    }
    // if setTimeout wasn't available but was latter defined
    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {
        cachedSetTimeout = setTimeout;
        return setTimeout(fun, 0);
    }
    try {
        // when when somebody has screwed with setTimeout but no I.E. maddness
        return cachedSetTimeout(fun, 0);
    } catch(e){
        try {
            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally
            return cachedSetTimeout.call(null, fun, 0);
        } catch(e){
            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error
            return cachedSetTimeout.call(this, fun, 0);
        }
    }


}
function runClearTimeout(marker) {
    if (cachedClearTimeout === clearTimeout) {
        //normal enviroments in sane situations
        return clearTimeout(marker);
    }
    // if clearTimeout wasn't available but was latter defined
    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {
        cachedClearTimeout = clearTimeout;
        return clearTimeout(marker);
    }
    try {
        // when when somebody has screwed with setTimeout but no I.E. maddness
        return cachedClearTimeout(marker);
    } catch (e){
        try {
            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally
            return cachedClearTimeout.call(null, marker);
        } catch (e){
            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.
            // Some versions of I.E. have different rules for clearTimeout vs setTimeout
            return cachedClearTimeout.call(this, marker);
        }
    }



}
var queue = [];
var draining = false;
var currentQueue;
var queueIndex = -1;

function cleanUpNextTick() {
    if (!draining || !currentQueue) {
        return;
    }
    draining = false;
    if (currentQueue.length) {
        queue = currentQueue.concat(queue);
    } else {
        queueIndex = -1;
    }
    if (queue.length) {
        drainQueue();
    }
}

function drainQueue() {
    if (draining) {
        return;
    }
    var timeout = runTimeout(cleanUpNextTick);
    draining = true;

    var len = queue.length;
    while(len) {
        currentQueue = queue;
        queue = [];
        while (++queueIndex < len) {
            if (currentQueue) {
                currentQueue[queueIndex].run();
            }
        }
        queueIndex = -1;
        len = queue.length;
    }
    currentQueue = null;
    draining = false;
    runClearTimeout(timeout);
}

process.nextTick = function (fun) {
    var args = new Array(arguments.length - 1);
    if (arguments.length > 1) {
        for (var i = 1; i < arguments.length; i++) {
            args[i - 1] = arguments[i];
        }
    }
    queue.push(new Item(fun, args));
    if (queue.length === 1 && !draining) {
        runTimeout(drainQueue);
    }
};

// v8 likes predictible objects
function Item(fun, array) {
    this.fun = fun;
    this.array = array;
}
Item.prototype.run = function () {
    this.fun.apply(null, this.array);
};
process.title = 'browser';
process.browser = true;
process.env = {};
process.argv = [];
process.version = ''; // empty string to avoid regexp issues
process.versions = {};

function noop() {}

process.on = noop;
process.addListener = noop;
process.once = noop;
process.off = noop;
process.removeListener = noop;
process.removeAllListeners = noop;
process.emit = noop;
process.prependListener = noop;
process.prependOnceListener = noop;

process.listeners = function (name) { return [] }

process.binding = function (name) {
    throw new Error('process.binding is not supported');
};

process.cwd = function () { return '/' };
process.chdir = function (dir) {
    throw new Error('process.chdir is not supported');
};
process.umask = function() { return 0; };


/***/ }),

/***/ "./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_ProgressBar_vue_vue_type_style_index_0_id_1c837bb2_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !!../../../../node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css */ "./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css");

            

var options = {};

options.insert = "head";
options.singleton = false;

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_ProgressBar_vue_vue_type_style_index_0_id_1c837bb2_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_1__["default"], options);



/* harmony default export */ __webpack_exports__["default"] = (_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_ProgressBar_vue_vue_type_style_index_0_id_1c837bb2_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_1__["default"].locals || {});

/***/ }),

/***/ "./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCardModule_vue_vue_type_style_index_0_id_5dd576c5_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !!../../../../node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css */ "./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css");

            

var options = {};

options.insert = "head";
options.singleton = false;

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCardModule_vue_vue_type_style_index_0_id_5dd576c5_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_1__["default"], options);



/* harmony default export */ __webpack_exports__["default"] = (_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCardModule_vue_vue_type_style_index_0_id_5dd576c5_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_1__["default"].locals || {});

/***/ }),

/***/ "./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_0_id_5f98c1fb_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !!../../../../node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css */ "./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css");

            

var options = {};

options.insert = "head";
options.singleton = false;

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_0_id_5f98c1fb_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_1__["default"], options);



/* harmony default export */ __webpack_exports__["default"] = (_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_0_id_5f98c1fb_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_1__["default"].locals || {});

/***/ }),

/***/ "./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js");
/* harmony import */ var _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_1_id_5f98c1fb_lang_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !!../../../../node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css */ "./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css");

            

var options = {};

options.insert = "head";
options.singleton = false;

var update = _node_modules_style_loader_dist_runtime_injectStylesIntoStyleTag_js__WEBPACK_IMPORTED_MODULE_0___default()(_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_1_id_5f98c1fb_lang_css__WEBPACK_IMPORTED_MODULE_1__["default"], options);



/* harmony default export */ __webpack_exports__["default"] = (_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_1_id_5f98c1fb_lang_css__WEBPACK_IMPORTED_MODULE_1__["default"].locals || {});

/***/ }),

/***/ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js":
/*!****************************************************************************!*\
  !*** ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js ***!
  \****************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";


var isOldIE = function isOldIE() {
  var memo;
  return function memorize() {
    if (typeof memo === 'undefined') {
      // Test for IE <= 9 as proposed by Browserhacks
      // @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805
      // Tests for existence of standard globals is to allow style-loader
      // to operate correctly into non-standard environments
      // @see https://github.com/webpack-contrib/style-loader/issues/177
      memo = Boolean(window && document && document.all && !window.atob);
    }

    return memo;
  };
}();

var getTarget = function getTarget() {
  var memo = {};
  return function memorize(target) {
    if (typeof memo[target] === 'undefined') {
      var styleTarget = document.querySelector(target); // Special case to return head of iframe instead of iframe itself

      if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {
        try {
          // This will throw an exception if access to iframe is blocked
          // due to cross-origin restrictions
          styleTarget = styleTarget.contentDocument.head;
        } catch (e) {
          // istanbul ignore next
          styleTarget = null;
        }
      }

      memo[target] = styleTarget;
    }

    return memo[target];
  };
}();

var stylesInDom = [];

function getIndexByIdentifier(identifier) {
  var result = -1;

  for (var i = 0; i < stylesInDom.length; i++) {
    if (stylesInDom[i].identifier === identifier) {
      result = i;
      break;
    }
  }

  return result;
}

function modulesToDom(list, options) {
  var idCountMap = {};
  var identifiers = [];

  for (var i = 0; i < list.length; i++) {
    var item = list[i];
    var id = options.base ? item[0] + options.base : item[0];
    var count = idCountMap[id] || 0;
    var identifier = "".concat(id, " ").concat(count);
    idCountMap[id] = count + 1;
    var index = getIndexByIdentifier(identifier);
    var obj = {
      css: item[1],
      media: item[2],
      sourceMap: item[3]
    };

    if (index !== -1) {
      stylesInDom[index].references++;
      stylesInDom[index].updater(obj);
    } else {
      stylesInDom.push({
        identifier: identifier,
        updater: addStyle(obj, options),
        references: 1
      });
    }

    identifiers.push(identifier);
  }

  return identifiers;
}

function insertStyleElement(options) {
  var style = document.createElement('style');
  var attributes = options.attributes || {};

  if (typeof attributes.nonce === 'undefined') {
    var nonce =  true ? __webpack_require__.nc : 0;

    if (nonce) {
      attributes.nonce = nonce;
    }
  }

  Object.keys(attributes).forEach(function (key) {
    style.setAttribute(key, attributes[key]);
  });

  if (typeof options.insert === 'function') {
    options.insert(style);
  } else {
    var target = getTarget(options.insert || 'head');

    if (!target) {
      throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");
    }

    target.appendChild(style);
  }

  return style;
}

function removeStyleElement(style) {
  // istanbul ignore if
  if (style.parentNode === null) {
    return false;
  }

  style.parentNode.removeChild(style);
}
/* istanbul ignore next  */


var replaceText = function replaceText() {
  var textStore = [];
  return function replace(index, replacement) {
    textStore[index] = replacement;
    return textStore.filter(Boolean).join('\n');
  };
}();

function applyToSingletonTag(style, index, remove, obj) {
  var css = remove ? '' : obj.media ? "@media ".concat(obj.media, " {").concat(obj.css, "}") : obj.css; // For old IE

  /* istanbul ignore if  */

  if (style.styleSheet) {
    style.styleSheet.cssText = replaceText(index, css);
  } else {
    var cssNode = document.createTextNode(css);
    var childNodes = style.childNodes;

    if (childNodes[index]) {
      style.removeChild(childNodes[index]);
    }

    if (childNodes.length) {
      style.insertBefore(cssNode, childNodes[index]);
    } else {
      style.appendChild(cssNode);
    }
  }
}

function applyToTag(style, options, obj) {
  var css = obj.css;
  var media = obj.media;
  var sourceMap = obj.sourceMap;

  if (media) {
    style.setAttribute('media', media);
  } else {
    style.removeAttribute('media');
  }

  if (sourceMap && typeof btoa !== 'undefined') {
    css += "\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), " */");
  } // For old IE

  /* istanbul ignore if  */


  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    while (style.firstChild) {
      style.removeChild(style.firstChild);
    }

    style.appendChild(document.createTextNode(css));
  }
}

var singleton = null;
var singletonCounter = 0;

function addStyle(obj, options) {
  var style;
  var update;
  var remove;

  if (options.singleton) {
    var styleIndex = singletonCounter++;
    style = singleton || (singleton = insertStyleElement(options));
    update = applyToSingletonTag.bind(null, style, styleIndex, false);
    remove = applyToSingletonTag.bind(null, style, styleIndex, true);
  } else {
    style = insertStyleElement(options);
    update = applyToTag.bind(null, style, options);

    remove = function remove() {
      removeStyleElement(style);
    };
  }

  update(obj);
  return function updateStyle(newObj) {
    if (newObj) {
      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap) {
        return;
      }

      update(obj = newObj);
    } else {
      remove();
    }
  };
}

module.exports = function (list, options) {
  options = options || {}; // Force single-tag solution on IE6-9, which has a hard limit on the # of <style>
  // tags it will allow on a page

  if (!options.singleton && typeof options.singleton !== 'boolean') {
    options.singleton = isOldIE();
  }

  list = list || [];
  var lastIdentifiers = modulesToDom(list, options);
  return function update(newList) {
    newList = newList || [];

    if (Object.prototype.toString.call(newList) !== '[object Array]') {
      return;
    }

    for (var i = 0; i < lastIdentifiers.length; i++) {
      var identifier = lastIdentifiers[i];
      var index = getIndexByIdentifier(identifier);
      stylesInDom[index].references--;
    }

    var newLastIdentifiers = modulesToDom(newList, options);

    for (var _i = 0; _i < lastIdentifiers.length; _i++) {
      var _identifier = lastIdentifiers[_i];

      var _index = getIndexByIdentifier(_identifier);

      if (stylesInDom[_index].references === 0) {
        stylesInDom[_index].updater();

        stylesInDom.splice(_index, 1);
      }
    }

    lastIdentifiers = newLastIdentifiers;
  };
};

/***/ }),

/***/ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js":
/*!********************************************************************!*\
  !*** ./node_modules/vue-loader/lib/runtime/componentNormalizer.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ normalizeComponent; }
/* harmony export */ });
/* globals __VUE_SSR_CONTEXT__ */

// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).
// This module is a runtime utility for cleaner component module output and will
// be included in the final webpack user bundle.

function normalizeComponent(
  scriptExports,
  render,
  staticRenderFns,
  functionalTemplate,
  injectStyles,
  scopeId,
  moduleIdentifier /* server only */,
  shadowMode /* vue-cli only */
) {
  // Vue.extend constructor export interop
  var options =
    typeof scriptExports === 'function' ? scriptExports.options : scriptExports

  // render functions
  if (render) {
    options.render = render
    options.staticRenderFns = staticRenderFns
    options._compiled = true
  }

  // functional template
  if (functionalTemplate) {
    options.functional = true
  }

  // scopedId
  if (scopeId) {
    options._scopeId = 'data-v-' + scopeId
  }

  var hook
  if (moduleIdentifier) {
    // server build
    hook = function (context) {
      // 2.3 injection
      context =
        context || // cached call
        (this.$vnode && this.$vnode.ssrContext) || // stateful
        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional
      // 2.2 with runInNewContext: true
      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {
        context = __VUE_SSR_CONTEXT__
      }
      // inject component styles
      if (injectStyles) {
        injectStyles.call(this, context)
      }
      // register component module identifier for async chunk inferrence
      if (context && context._registeredComponents) {
        context._registeredComponents.add(moduleIdentifier)
      }
    }
    // used by ssr in case component is cached and beforeCreate
    // never gets called
    options._ssrRegister = hook
  } else if (injectStyles) {
    hook = shadowMode
      ? function () {
          injectStyles.call(
            this,
            (options.functional ? this.parent : this).$root.$options.shadowRoot
          )
        }
      : injectStyles
  }

  if (hook) {
    if (options.functional) {
      // for template-only hot-reload because in that case the render fn doesn't
      // go through the normalizer
      options._injectStyles = hook
      // register for functional component in vue file
      var originalRender = options.render
      options.render = function renderWithStyleInjection(h, context) {
        hook.call(context)
        return originalRender(h, context)
      }
    } else {
      // inject component registration as beforeCreate hook
      var existing = options.beforeCreate
      options.beforeCreate = existing ? [].concat(existing, hook) : [hook]
    }
  }

  return {
    exports: scriptExports,
    options: options
  }
}


/***/ }),

/***/ "./resources/assets/js/component/ProgressBar.vue":
/*!*******************************************************!*\
  !*** ./resources/assets/js/component/ProgressBar.vue ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _ProgressBar_vue_vue_type_template_id_1c837bb2_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ProgressBar.vue?vue&type=template&id=1c837bb2&scoped=true */ "./resources/assets/js/component/ProgressBar.vue?vue&type=template&id=1c837bb2&scoped=true");
/* harmony import */ var _ProgressBar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ProgressBar.vue?vue&type=script&lang=js */ "./resources/assets/js/component/ProgressBar.vue?vue&type=script&lang=js");
/* harmony import */ var _ProgressBar_vue_vue_type_style_index_0_id_1c837bb2_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css */ "./resources/assets/js/component/ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");



;


/* normalize component */

var component = (0,_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _ProgressBar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _ProgressBar_vue_vue_type_template_id_1c837bb2_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _ProgressBar_vue_vue_type_template_id_1c837bb2_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "1c837bb2",
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/assets/js/component/ProgressBar.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/assets/js/component/ProgressBar.vue?vue&type=script&lang=js":
/*!*******************************************************************************!*\
  !*** ./resources/assets/js/component/ProgressBar.vue?vue&type=script&lang=js ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ProgressBar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_ProgressBar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/assets/js/component/ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css":
/*!***************************************************************************************************************!*\
  !*** ./resources/assets/js/component/ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css ***!
  \***************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_style_loader_dist_cjs_js_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_ProgressBar_vue_vue_type_style_index_0_id_1c837bb2_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/style-loader/dist/cjs.js!../../../../node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css */ "./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=style&index=0&id=1c837bb2&scoped=true&lang=css");


/***/ }),

/***/ "./resources/assets/js/component/ProgressBar.vue?vue&type=template&id=1c837bb2&scoped=true":
/*!*************************************************************************************************!*\
  !*** ./resources/assets/js/component/ProgressBar.vue?vue&type=template&id=1c837bb2&scoped=true ***!
  \*************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_ProgressBar_vue_vue_type_template_id_1c837bb2_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_ProgressBar_vue_vue_type_template_id_1c837bb2_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_ProgressBar_vue_vue_type_template_id_1c837bb2_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./ProgressBar.vue?vue&type=template&id=1c837bb2&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/component/ProgressBar.vue?vue&type=template&id=1c837bb2&scoped=true");


/***/ }),

/***/ "./resources/assets/js/components/FlashCardModule.vue":
/*!************************************************************!*\
  !*** ./resources/assets/js/components/FlashCardModule.vue ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _FlashCardModule_vue_vue_type_template_id_5dd576c5_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./FlashCardModule.vue?vue&type=template&id=5dd576c5&scoped=true */ "./resources/assets/js/components/FlashCardModule.vue?vue&type=template&id=5dd576c5&scoped=true");
/* harmony import */ var _FlashCardModule_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./FlashCardModule.vue?vue&type=script&lang=js */ "./resources/assets/js/components/FlashCardModule.vue?vue&type=script&lang=js");
/* harmony import */ var _FlashCardModule_vue_vue_type_style_index_0_id_5dd576c5_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css */ "./resources/assets/js/components/FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! !../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");



;


/* normalize component */

var component = (0,_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _FlashCardModule_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _FlashCardModule_vue_vue_type_template_id_5dd576c5_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _FlashCardModule_vue_vue_type_template_id_5dd576c5_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "5dd576c5",
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/assets/js/components/FlashCardModule.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/assets/js/components/FlashCardModule.vue?vue&type=script&lang=js":
/*!************************************************************************************!*\
  !*** ./resources/assets/js/components/FlashCardModule.vue?vue&type=script&lang=js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCardModule_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FlashCardModule.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCardModule_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/assets/js/components/FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css":
/*!********************************************************************************************************************!*\
  !*** ./resources/assets/js/components/FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css ***!
  \********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_style_loader_dist_cjs_js_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCardModule_vue_vue_type_style_index_0_id_5dd576c5_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/style-loader/dist/cjs.js!../../../../node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css */ "./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=style&index=0&id=5dd576c5&scoped=true&lang=css");


/***/ }),

/***/ "./resources/assets/js/components/FlashCardModule.vue?vue&type=template&id=5dd576c5&scoped=true":
/*!******************************************************************************************************!*\
  !*** ./resources/assets/js/components/FlashCardModule.vue?vue&type=template&id=5dd576c5&scoped=true ***!
  \******************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCardModule_vue_vue_type_template_id_5dd576c5_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCardModule_vue_vue_type_template_id_5dd576c5_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCardModule_vue_vue_type_template_id_5dd576c5_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FlashCardModule.vue?vue&type=template&id=5dd576c5&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/components/FlashCardModule.vue?vue&type=template&id=5dd576c5&scoped=true");


/***/ }),

/***/ "./resources/assets/js/course/components/SearchBar.vue":
/*!*************************************************************!*\
  !*** ./resources/assets/js/course/components/SearchBar.vue ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _SearchBar_vue_vue_type_template_id_4a58c6b8__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SearchBar.vue?vue&type=template&id=4a58c6b8 */ "./resources/assets/js/course/components/SearchBar.vue?vue&type=template&id=4a58c6b8");
/* harmony import */ var _SearchBar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SearchBar.vue?vue&type=script&lang=js */ "./resources/assets/js/course/components/SearchBar.vue?vue&type=script&lang=js");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */
;
var component = (0,_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _SearchBar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _SearchBar_vue_vue_type_template_id_4a58c6b8__WEBPACK_IMPORTED_MODULE_0__.render,
  _SearchBar_vue_vue_type_template_id_4a58c6b8__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/assets/js/course/components/SearchBar.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/assets/js/course/components/SearchBar.vue?vue&type=script&lang=js":
/*!*************************************************************************************!*\
  !*** ./resources/assets/js/course/components/SearchBar.vue?vue&type=script&lang=js ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchBar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchBar.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/course/components/SearchBar.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchBar_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/assets/js/course/components/SearchBar.vue?vue&type=template&id=4a58c6b8":
/*!*******************************************************************************************!*\
  !*** ./resources/assets/js/course/components/SearchBar.vue?vue&type=template&id=4a58c6b8 ***!
  \*******************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchBar_vue_vue_type_template_id_4a58c6b8__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchBar_vue_vue_type_template_id_4a58c6b8__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_SearchBar_vue_vue_type_template_id_4a58c6b8__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./SearchBar.vue?vue&type=template&id=4a58c6b8 */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/course/components/SearchBar.vue?vue&type=template&id=4a58c6b8");


/***/ }),

/***/ "./resources/assets/js/module/flashcard.js":
/*!*************************************************!*\
  !*** ./resources/assets/js/module/flashcard.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var StackedCards = /*#__PURE__*/function () {
  function StackedCards() {
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    _classCallCheck(this, StackedCards);
    this.options = _objectSpread({
      stackedView: "Top",
      rotate: true,
      visibleItems: 3,
      margin: 10,
      useOverlays: true
    }, options);
    this.currentPosition = 0;
    this.isFirstTime = true;
    this.elTrans = 0;
    this.container = document.getElementById("stacked-cards-block");
    this.cardsContainer = this.container.querySelector(".stackedcards-container");
    this.cards = Array.from(this.cardsContainer.children);
    this.rightOverlay = this.container.querySelector(".stackedcards-overlay.right");
    this.leftOverlay = this.container.querySelector(".stackedcards-overlay.left");
    this.maxElements = this.cards.length;
    this.options.visibleItems = Math.min(this.options.visibleItems, this.maxElements);
    this.init();
  }
  return _createClass(StackedCards, [{
    key: "init",
    value: function init() {
      var _this = this;
      this.setupCards();
      this.cards.forEach(function (card) {
        console.log('card: ', card);
        // Remove existing click handlers
        card.removeEventListener('click', _this.handleCardClick);
      });
      this.addEventListeners();
      this.updateUI();
      setTimeout(function () {
        return _this.container.classList.remove("init");
      }, 150);
    }
  }, {
    key: "setupCards",
    value: function setupCards() {
      var _this2 = this;
      var _this$options = this.options,
        stackedView = _this$options.stackedView,
        visibleItems = _this$options.visibleItems,
        margin = _this$options.margin;
      var marginTotal = margin * (visibleItems - 1);
      this.cardsContainer.style.marginBottom = "".concat(marginTotal, "px");
      this.elTrans = stackedView === "Top" ? marginTotal : 0;
      this.cards.slice(visibleItems).forEach(function (card) {
        card.classList.add("stackedcards-".concat(stackedView.toLowerCase()), "stackedcards--animatable", "stackedcards-origin-".concat(stackedView.toLowerCase()));
        card.style.cssText = "\n        z-index: 0;\n        opacity: 0;\n        transform: scale(".concat(1 - visibleItems * 0.04, ") translateY(").concat(_this2.elTrans, "px);\n      ");
      });
      this.updateActiveCard();
      this.setupOverlays();
    }
  }, {
    key: "setupOverlays",
    value: function setupOverlays() {
      var _this3 = this;
      if (!this.options.useOverlays) {
        this.leftOverlay.classList.add("stackedcards-overlay-hidden");
        this.rightOverlay.classList.add("stackedcards-overlay-hidden");
        return;
      }
      [this.leftOverlay, this.rightOverlay].forEach(function (overlay) {
        overlay.style.transform = "translateY(".concat(_this3.elTrans, "px)");
      });
    }
  }, {
    key: "updateActiveCard",
    value: function updateActiveCard() {
      if (this.cards[this.currentPosition]) {
        this.cards[this.currentPosition].classList.add("stackedcards-active");
      }
    }
  }, {
    key: "addEventListeners",
    value: function addEventListeners() {
      var _this4 = this;
      // First remove any existing click handlers
      this.cards.forEach(function (card) {
        card.removeEventListener('click', _this4.handleCardClick);
      });

      // Store reference to click handler for proper removal
      this.handleCardClick = function (e) {
        console.log("e: ", e);
        var targetElement = e.target;
        var shouldFlip = true;
        while (targetElement && targetElement !== e.currentTarget) {
          if (targetElement.classList.contains("noFlip") || targetElement.classList.contains("card_audio") || targetElement.classList.contains("underline") || targetElement.tagName === "BUTTON" || targetElement.tagName === "A" || targetElement.tagName === "SVG" || targetElement.tagName === "path" || targetElement.closest('.card-footer') !== null || targetElement.closest('[class*="cursor-pointer"]') !== null) {
            shouldFlip = false;
            break;
          }
          targetElement = targetElement.parentElement;
        }
        console.log("shouldFlip: ", shouldFlip);
        if (shouldFlip) {
          var version = "development";
          // kiểm tra mặt truowcss khi lật nếu là mặt trước
          if (!e.currentTarget.querySelector(".card-inner").classList.contains("flip") && [39, 40].includes(course_id) && parseInt(authUser.isTester) === 0 && ["production", "prod"].includes(version)) {
            var event = parseInt(course_id) === 39 ? "n5_flc_behind" : "n4_flc_behind";

            // check function ga() co tai khong
            if (typeof ga !== 'undefined') {
              ga("send", "event", "nx_flc_behind", event, event);
            }
          }
          e.currentTarget.querySelector(".card-inner").classList.toggle("flip");
        }
      };

      // Add new click handlers
      this.cards.forEach(function (card) {
        card.addEventListener("click", _this4.handleCardClick);
      });
    }
  }, {
    key: "swipeLeft",
    value: function swipeLeft() {
      var _this5 = this;
      if (this.currentPosition >= this.maxElements) {
        return;
      }
      this.transformCard(-1000, 0, 0);
      if (this.options.useOverlays) {
        this.transformOverlay(this.leftOverlay, -1000, 0, 1, function () {
          return _this5.resetOverlay(_this5.leftOverlay);
        });
      }
      this.nextCard();
    }
  }, {
    key: "swipeRight",
    value: function swipeRight() {
      var _this6 = this;
      if (this.currentPosition >= this.maxElements) {
        return;
      }
      this.transformCard(1000, 0, 0);
      if (this.options.useOverlays) {
        this.transformOverlay(this.rightOverlay, 1000, 0, 1, function () {
          return _this6.resetOverlay(_this6.rightOverlay);
        });
      }
      this.nextCard();
    }
  }, {
    key: "undo",
    value: function undo() {
      if (this.currentPosition <= 0) {
        return;
      }
      this.currentPosition--;
      this.updateUI();
      this.updateActiveCard();
    }
  }, {
    key: "nextCard",
    value: function nextCard() {
      this.currentPosition++;
      this.updateUI();
      this.updateActiveCard();
    }
  }, {
    key: "transformCard",
    value: function transformCard(x, y, opacity) {
      var card = this.cards[this.currentPosition];
      if (!card) {
        return;
      }
      card.classList.remove("no-transition");
      card.style.zIndex = 6;
      this.transformElement(card, x, y, opacity);
    }
  }, {
    key: "transformOverlay",
    value: function transformOverlay(overlay, x, y, opacity, callback) {
      overlay.classList.remove("no-transition");
      overlay.style.zIndex = 8;
      this.transformElement(overlay, x, y, opacity);
      setTimeout(callback, 300);
    }
  }, {
    key: "resetOverlay",
    value: function resetOverlay(overlay) {
      var _this7 = this;
      overlay.classList.add("no-transition");
      requestAnimationFrame(function () {
        overlay.style.transform = "translateY(".concat(_this7.elTrans, "px)");
        overlay.style.opacity = 0;
      });
      this.isFirstTime = false;
    }
  }, {
    key: "transformElement",
    value: function transformElement(element, x, y, opacity) {
      var _this8 = this;
      var rotate = this.options.rotate ? Math.min(Math.max(x / 10, -15), 15) : 0;
      requestAnimationFrame(function () {
        element.style.transform = "translateX(".concat(x, "px) translateY(").concat(y + _this8.elTrans, "px) rotate(").concat(rotate, "deg)");
        element.style.opacity = opacity;
      });
    }
  }, {
    key: "updateUI",
    value: function updateUI() {
      var _this9 = this;
      requestAnimationFrame(function () {
        var _this9$options = _this9.options,
          visibleItems = _this9$options.visibleItems,
          margin = _this9$options.margin;
        var zIndex = 5;
        var scale;
        _this9.cards.slice(_this9.currentPosition, _this9.currentPosition + visibleItems).forEach(function (card, i) {
          scale = zIndex === 4 ? "scale(0.97)" : zIndex === 3 ? "scale(0.95)" : "";
          card.style.cssText = "\n          z-index: ".concat(zIndex--, ";\n          opacity: 1;\n          transform: ").concat(scale, " translateY(").concat(margin * i * (1 - visibleItems * 0.04), "px);\n        ");
          card.classList.add("stackedcards--animatable");
        });
      });
    }
  }, {
    key: "destroy",
    value: function destroy() {
      // Remove event listeners to prevent memory leaks
      this.cards.forEach(function (card) {
        var cardInner = card.querySelector(".card-inner");
        if (cardInner) {
          var newCard = card.cloneNode(true);
          card.parentNode.replaceChild(newCard, card);
        }
      });
    }
  }]);
}();
/* harmony default export */ __webpack_exports__["default"] = (StackedCards);

/***/ }),

/***/ "./resources/assets/js/vocabulary/Index.vue":
/*!**************************************************!*\
  !*** ./resources/assets/js/vocabulary/Index.vue ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Index_vue_vue_type_template_id_5f98c1fb_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Index.vue?vue&type=template&id=5f98c1fb&scoped=true */ "./resources/assets/js/vocabulary/Index.vue?vue&type=template&id=5f98c1fb&scoped=true");
/* harmony import */ var _Index_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Index.vue?vue&type=script&lang=js */ "./resources/assets/js/vocabulary/Index.vue?vue&type=script&lang=js");
/* harmony import */ var _Index_vue_vue_type_style_index_0_id_5f98c1fb_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css */ "./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css");
/* harmony import */ var _Index_vue_vue_type_style_index_1_id_5f98c1fb_lang_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css */ "./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! !../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");



;



/* normalize component */

var component = (0,_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_4__["default"])(
  _Index_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _Index_vue_vue_type_template_id_5f98c1fb_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _Index_vue_vue_type_template_id_5f98c1fb_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "5f98c1fb",
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/assets/js/vocabulary/Index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/assets/js/vocabulary/Index.vue?vue&type=script&lang=js":
/*!**************************************************************************!*\
  !*** ./resources/assets/js/vocabulary/Index.vue?vue&type=script&lang=js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css":
/*!**********************************************************************************************************!*\
  !*** ./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css ***!
  \**********************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_style_loader_dist_cjs_js_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_0_id_5f98c1fb_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/style-loader/dist/cjs.js!../../../../node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css */ "./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=0&id=5f98c1fb&scoped=true&lang=css");


/***/ }),

/***/ "./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css":
/*!**********************************************************************************************!*\
  !*** ./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css ***!
  \**********************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_style_loader_dist_cjs_js_node_modules_laravel_mix_node_modules_css_loader_dist_cjs_js_clonedRuleSet_15_use_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_clonedRuleSet_15_use_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_style_index_1_id_5f98c1fb_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/style-loader/dist/cjs.js!../../../../node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css */ "./node_modules/style-loader/dist/cjs.js!./node_modules/laravel-mix/node_modules/css-loader/dist/cjs.js??clonedRuleSet-15.use[1]!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/dist/cjs.js??clonedRuleSet-15.use[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=style&index=1&id=5f98c1fb&lang=css");


/***/ }),

/***/ "./resources/assets/js/vocabulary/Index.vue?vue&type=template&id=5f98c1fb&scoped=true":
/*!********************************************************************************************!*\
  !*** ./resources/assets/js/vocabulary/Index.vue?vue&type=template&id=5f98c1fb&scoped=true ***!
  \********************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_template_id_5f98c1fb_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_template_id_5f98c1fb_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_Index_vue_vue_type_template_id_5f98c1fb_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Index.vue?vue&type=template&id=5f98c1fb&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/Index.vue?vue&type=template&id=5f98c1fb&scoped=true");


/***/ }),

/***/ "./resources/assets/js/vocabulary/components/FlashCard.vue":
/*!*****************************************************************!*\
  !*** ./resources/assets/js/vocabulary/components/FlashCard.vue ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _FlashCard_vue_vue_type_template_id_9585aae4_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./FlashCard.vue?vue&type=template&id=9585aae4&scoped=true */ "./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=template&id=9585aae4&scoped=true");
/* harmony import */ var _FlashCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./FlashCard.vue?vue&type=script&lang=js */ "./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=script&lang=js");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! !../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */
;
var component = (0,_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _FlashCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_1__["default"],
  _FlashCard_vue_vue_type_template_id_9585aae4_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render,
  _FlashCard_vue_vue_type_template_id_9585aae4_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns,
  false,
  null,
  "9585aae4",
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/assets/js/vocabulary/components/FlashCard.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=script&lang=js":
/*!*****************************************************************************************!*\
  !*** ./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=script&lang=js ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FlashCard.vue?vue&type=script&lang=js */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=script&lang=js");
 /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCard_vue_vue_type_script_lang_js__WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=template&id=9585aae4&scoped=true":
/*!***********************************************************************************************************!*\
  !*** ./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=template&id=9585aae4&scoped=true ***!
  \***********************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   render: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCard_vue_vue_type_template_id_9585aae4_scoped_true__WEBPACK_IMPORTED_MODULE_0__.render; },
/* harmony export */   staticRenderFns: function() { return /* reexport safe */ _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCard_vue_vue_type_template_id_9585aae4_scoped_true__WEBPACK_IMPORTED_MODULE_0__.staticRenderFns; }
/* harmony export */ });
/* harmony import */ var _node_modules_babel_loader_lib_index_js_clonedRuleSet_5_use_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ruleSet_1_rules_2_node_modules_vue_loader_lib_index_js_vue_loader_options_FlashCard_vue_vue_type_template_id_9585aae4_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!../../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./FlashCard.vue?vue&type=template&id=9585aae4&scoped=true */ "./node_modules/babel-loader/lib/index.js??clonedRuleSet-5.use[0]!./node_modules/vue-loader/lib/loaders/templateLoader.js??ruleSet[1].rules[2]!./node_modules/vue-loader/lib/index.js??vue-loader-options!./resources/assets/js/vocabulary/components/FlashCard.vue?vue&type=template&id=9585aae4&scoped=true");


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	!function() {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = function(module) {
/******/ 			var getter = module && module.__esModule ?
/******/ 				function() { return module['default']; } :
/******/ 				function() { return module; };
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/nonce */
/******/ 	!function() {
/******/ 		__webpack_require__.nc = undefined;
/******/ 	}();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
!function() {
/*!******************************************************!*\
  !*** ./resources/assets/js/vocabulary/vocabulary.js ***!
  \******************************************************/
new Vue({
  el: "#vocabulary_content",
  data: {
    list_vocabulary: list_vocabulary
  },
  components: {
    vocabulary: (__webpack_require__(/*! ./Index.vue */ "./resources/assets/js/vocabulary/Index.vue")["default"])
  },
  methods: {
    showContent: function showContent(type) {
      //hiển thị nội dung của section
      $(".content-section").removeClass("active");
      $("#content-" + type).addClass("active");

      //ẩn menu
      $("#navMenu").removeClass("active");
    }
  }
});
}();
/******/ })()
;