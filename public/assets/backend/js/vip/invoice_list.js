"use strict";function _typeof(t){"@babel/helpers - typeof";return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _regeneratorRuntime(){function t(t,e,o){return Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}),t[e]}function e(t,e,o,i){var r=e&&e.prototype instanceof n?e:n,a=Object.create(r.prototype),c=new f(i||[]);return y(a,"_invoke",{value:s(t,o,c)}),a}function o(t,e,o){try{return{type:"normal",arg:t.call(e,o)}}catch(t){return{type:"throw",arg:t}}}function n(){}function i(){}function r(){}function a(e){["next","throw","return"].forEach(function(o){t(e,o,function(t){return this._invoke(o,t)})})}function c(t,e){function n(i,r,a,c){var s=o(t[i],t,r);if("throw"!==s.type){var u=s.arg,d=u.value;return d&&"object"==_typeof(d)&&g.call(d,"__await")?e.resolve(d.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(d).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}var i;y(this,"_invoke",{value:function(t,o){function r(){return new e(function(e,i){n(t,o,e,i)})}return i=i?i.then(r,r):r()}})}function s(t,e,n){var i=k;return function(r,a){if(i===L)throw Error("Generator is already running");if(i===S){if("throw"===r)throw a;return{value:p,done:!0}}for(n.method=r,n.arg=a;;){var c=n.delegate;if(c){var s=u(c,n);if(s){if(s===C)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===k)throw i=S,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=L;var d=o(t,e,n);if("normal"===d.type){if(i=n.done?S:D,d.arg===C)continue;return{value:d.arg,done:n.done}}"throw"===d.type&&(i=S,n.method="throw",n.arg=d.arg)}}}function u(t,e){var n=e.method,i=t.iterator[n];if(i===p)return e.delegate=null,"throw"===n&&t.iterator["return"]&&(e.method="return",e.arg=p,u(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),C;var r=o(i,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,C;var a=r.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=p),e.delegate=null,C):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,C)}function d(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function l(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function f(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(d,this),this.reset(!0)}function h(t){if(t||""===t){var e=t[_];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,n=function i(){for(;++o<t.length;)if(g.call(t,o))return i.value=t[o],i.done=!1,i;return i.value=p,i.done=!0,i};return n.next=n}}throw new TypeError(_typeof(t)+" is not iterable")}_regeneratorRuntime=function(){return v};var p,v={},m=Object.prototype,g=m.hasOwnProperty,y=Object.defineProperty||function(t,e,o){t[e]=o.value},b="function"==typeof Symbol?Symbol:{},_=b.iterator||"@@iterator",I=b.asyncIterator||"@@asyncIterator",w=b.toStringTag||"@@toStringTag";try{t({},"")}catch(p){t=function(t,e,o){return t[e]=o}}v.wrap=e;var k="suspendedStart",D="suspendedYield",L="executing",S="completed",C={},N={};t(N,_,function(){return this});var O=Object.getPrototypeOf,A=O&&O(O(h([])));A&&A!==m&&g.call(A,_)&&(N=A);var j=r.prototype=n.prototype=Object.create(N);return i.prototype=r,y(j,"constructor",{value:r,configurable:!0}),y(r,"constructor",{value:i,configurable:!0}),i.displayName=t(r,w,"GeneratorFunction"),v.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===i||"GeneratorFunction"===(e.displayName||e.name))},v.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,r):(e.__proto__=r,t(e,w,"GeneratorFunction")),e.prototype=Object.create(j),e},v.awrap=function(t){return{__await:t}},a(c.prototype),t(c.prototype,I,function(){return this}),v.AsyncIterator=c,v.async=function(t,o,n,i,r){void 0===r&&(r=Promise);var a=new c(e(t,o,n,i),r);return v.isGeneratorFunction(o)?a:a.next().then(function(t){return t.done?t.value:a.next()})},a(j),t(j,w,"Generator"),t(j,_,function(){return this}),t(j,"toString",function(){return"[object Generator]"}),v.keys=function(t){var e=Object(t),o=[];for(var n in e)o.push(n);return o.reverse(),function i(){for(;o.length;){var t=o.pop();if(t in e)return i.value=t,i.done=!1,i}return i.done=!0,i}},v.values=h,f.prototype={constructor:f,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=p,this.done=!1,this.delegate=null,this.method="next",this.arg=p,this.tryEntries.forEach(l),!t)for(var e in this)"t"===e.charAt(0)&&g.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=p)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){function e(e,n){return r.type="throw",r.arg=t,o.next=e,n&&(o.method="next",o.arg=p),!!n}if(this.done)throw t;for(var o=this,n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],r=i.completion;if("root"===i.tryLoc)return e("end");if(i.tryLoc<=this.prev){var a=g.call(i,"catchLoc"),c=g.call(i,"finallyLoc");if(a&&c){if(this.prev<i.catchLoc)return e(i.catchLoc,!0);if(this.prev<i.finallyLoc)return e(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return e(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return e(i.finallyLoc)}}}},abrupt:function(t,e){for(var o=this.tryEntries.length-1;o>=0;--o){var n=this.tryEntries[o];if(n.tryLoc<=this.prev&&g.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var r=i?i.completion:{};return r.type=t,r.arg=e,i?(this.method="next",this.next=i.finallyLoc,C):this.complete(r)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),C},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.finallyLoc===t)return this.complete(o.completion,o.afterLoc),l(o),C}},"catch":function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e];if(o.tryLoc===t){var n=o.completion;if("throw"===n.type){var i=n.arg;l(o)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,e,o){return this.delegate={iterator:h(t),resultName:e,nextLoc:o},"next"===this.method&&(this.arg=p),C}},v}function asyncGeneratorStep(t,e,o,n,i,r,a){try{var c=t[r](a),s=c.value}catch(t){return void o(t)}c.done?e(s):Promise.resolve(s).then(n,i)}function _asyncToGenerator(t){return function(){var e=this,o=arguments;return new Promise(function(n,i){function r(t){asyncGeneratorStep(c,n,i,r,a,"next",t)}function a(t){asyncGeneratorStep(c,n,i,r,a,"throw",t)}var c=t.apply(e,o);r(void 0)})}}function _createForOfIteratorHelper(t,e){var o="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!o){if(Array.isArray(t)||(o=_unsupportedIterableToArray(t))||e&&t&&"number"==typeof t.length){o&&(t=o);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,c=!1;return{s:function(){o=o.call(t)},n:function(){var t=o.next();return a=t.done,t},e:function(t){c=!0,r=t},f:function(){try{a||null==o["return"]||o["return"]()}finally{if(c)throw r}}}}function ownKeys(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,n)}return o}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(o),!0).forEach(function(e){_defineProperty(t,e,o[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(o)):ownKeys(Object(o)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(o,e))})}return t}function _defineProperty(t,e,o){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t}function _toPropertyKey(t){var e=_toPrimitive(t,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var o=t[Symbol.toPrimitive];if(void 0!==o){var n=o.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray(t,e);var o={}.toString.call(t).slice(8,-1);return"Object"===o&&t.constructor&&(o=t.constructor.name),"Map"===o||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_arrayLikeToArray(t,e):void 0}}function _iterableToArray(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray(t)}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,n=Array(e);o<e;o++)n[o]=t[o];return n}function convert_block_three(t){if("000"==t)return"";var e=t+"";switch(e.length){case 0:return"";case 1:return chuHangDonVi[e];case 2:return convert_block_two(e);case 3:var o="";"00"!=e.slice(1,3)&&(o=convert_block_two(e.slice(1,3)));var n=chuHangTram[e[0]]+" trăm";return n+" "+o}}function convert_block_two(t){var e=chuHangDonVi[t[1]],o=chuHangChuc[t[0]],n="";return t[0]>0&&5==t[1]&&(e="lăm"),t[0]>1&&(n=" mươi",1==t[1]&&(e=" mốt")),o+""+n+" "+e}function to_vietnamese(t){var e=parseInt(t)+"",o=0,n=[],i=e.length,r=[],a="";if(0==i||"NaN"==e)return"";for(;i>=0;)n.push(e.substring(i,Math.max(i-3,0))),i-=3;for(o=n.length-1;o>=0;o--)""!=n[o]&&"000"!=n[o]&&(r.push(convert_block_three(n[o])),dvBlock[o]&&r.push(dvBlock[o]));return a=r.join(" "),a.replace(/[0-9]/g,"").replace(/ /g," ").replace(/ $/,"")}var defaultNumbers=" hai ba bốn năm sáu bảy tám chín",chuHangDonVi=("1 một"+defaultNumbers).split(" "),chuHangChuc=("lẻ mười"+defaultNumbers).split(" "),chuHangTram=("không một"+defaultNumbers).split(" "),dvBlock="1 nghìn triệu tỷ".split(" ");$.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),Vue.component("v-select",VueSelect.VueSelect);var router=new VueRouter({mode:"history"});Vue.filter("dateTimeToMinute",function(t){return t?moment(t).format("HH:mm:ss DD/MM/YYYY"):"--"}),Vue.filter("readableDate",function(t){return t?moment(t).format("DD/MM/YYYY"):"--"}),Vue.filter("decimal",function(t){return new Intl.NumberFormat("vi-VN").format(t)}),Vue.use(DatePicker);var invoiceScreen=new Vue({router:router,el:"#vip-invoice--screen",components:{paginate:VuejsPaginate},data:function(){return{url:window.location.origin,adminId:adminId,loading:!1,addUserLoading:!1,activeLoading:!1,activeInvoiceLoading:!1,createInvoiceLoading:!1,showAddInvoiceForm:!1,showNoteModal:!1,showLogModal:!1,showAddGroupModal:!1,showInvoiceDept:!1,depositInvoice:{},currentNote:"",currentLog:null,currentAddCourseData:{},joinedGroupListByUser:[],groupListByUser:[],groupListByCombo:[],offlineGroups:[],joinedOfflineGroups:[],search:"",filterCount:0,filter:{id:"",uuid:"",email:"",phone:"",name:"",comboId:[],courseId:void 0,groupId:void 0,paymentMethod:void 0,time_by:void 0,time_from:void 0,time_to:void 0,sort:"",sale:void 0,status:"new",isVip:void 0,voucher:void 0,admin_create:void 0,state:void 0,isAttached:void 0,isActivated:void 0,currency:void 0,refund:"",location:"",payer:"",modifyStatus:"",exportStatus:"",depositStatus:"",page:1,per_page:20,total_page:10},results:[],total_result:0,currentFormUser:null,formError:{},formData:{email:"",password:"",fullname:"",phone:"",facebook:"",chat:"",address:"",department:"1",comboId:null,comboType:[],price:void 0,currency:"vnd",coupon:"",paymentMethod:"",paidAt:moment().format("YYYY-MM-DD HH:mm:ss"),note:void 0,groupId:[],isVip:!1,isOnline:!0,activeOnline:!1,discountMoney:0,paidMoney:"",bookIds:[],refundBooks:[],rewardId:void 0,oldPrice:void 0,paidFor:void 0,bonusComboId:void 0},bookData:{postCode:"",address:"",price:0,payer:"dmr",totalPrice:0,location:"",status:"packing",refundFault:"",refundShip:0,modifyStatus:""},bookList:[],reservations:[],showInvoiceDetail:!1,printInvoice:{},cloneInvoice:!1,offlineData:{gift:{bag:!1,book:!1}},userAvailableRewards:[],bonus:{courseId:"",days:1},bonuses:[{courseId:"",days:1}],courses:[],editingInvoice:null,validCourses:[{id:17,name:"N1"},{id:16,name:"N2"},{id:3,name:"N3"},{id:40,name:"N4"},{id:39,name:"N5"}],byPassGroupLevel:0}},computed:{relevantInvoices:function(){return this.depositInvoice.paid_for_id&&this.depositInvoice.deposit_invoice?[this.depositInvoice.deposit_invoice].concat(_toConsumableArray(this.depositInvoice.deposit_invoice.relevant_invoices)):[this.depositInvoice].concat(_toConsumableArray(this.depositInvoice.relevant_invoices))},isOfflineFilter:function(){return this.$route.query.isVip&&3===Number(this.$route.query.isVip)},isOfflineInvoice:function(){return"offline"===this.currentAddCourseData.invoice.product_type},joinedList:function(){var t=this;return this.isOfflineInvoice?this.joinedGroupListByUser:this.joinedGroupListByUser.map(function(e){var o=t.reservations.findIndex(function(t){return t.id===e.id});return e.userCount=o===-1?e.group_user.length:t.reservations[o].count,e})},groupList:function(){var t=this;return this.isOfflineInvoice?this.groupListByUser:this.groupListByUser.map(function(e){var o=t.reservations.findIndex(function(t){return t.id===e.id});return e.userCount=o===-1?e.group_user.length:t.reservations[o].count,e})},groupListCombo:function(){var t=this;return this.groupListByCombo.map(function(e){var o=t.reservations.findIndex(function(t){return t.id===e.id});return e.userCount=o===-1?e.group_user.length:t.reservations[o].count,e})},subjects:function(){return{invoice_creation:"Tạo đơn",user_id_reservation:"Sửa email sau khi giữ chỗ",user_attachment:"Thêm vào lớp",user_detachment:"Xoá khỏi lớp",information_changed:"Thay đổi thông tin",online_course_activation:"Kích hoạt khoá online",active_bonus:"Kích hoạt khoá tặng kèm"}},columns:function(){return{invoice:{id:"ID",info_contact:{name:"Tên học viên",phone:"Số điện thoại",email:"Email",facebook:"Link facebook",chat:"Link chăm sóc",address:"Địa chỉ"},price:"Giá",currency:"Đơn vị",discount_money:"Chiết khấu",payment_method_id:"Phương thức thanh toán",department_id:"Cơ sở",paid_money:"Thực thu",paid_at:"Ngày thanh toán",note:"Note",books:"Sách",combo:"Sản phẩm",bonus:"Quà tặng",book_info:{postCode:"(Sách) Mã bưu điện",address:"(Sách) Địa chỉ",price:"(Sách) giá",payer:"(Sách) Người trả phí vc",totalPrice:"(Sách) totalPrice",phone:"(Sách) SĐT",status:"(Sách) Trạng thái",location:"(Sách) Vị trí",refundFault:"(Sách) Lỗi do",refundShip:"(Sách) Phí ship hoàn",modifyStatus:"(Sách) Trạng thái sách",exportStatus:"(Sách) Trạng thái in",exportDate:"(Sách) Ngày in"},refund_books:"Hoàn sách"},community_group_user:{group_id:"ID nhóm",user_id:"ID học viên"},course_owner:{watch_expired_day:"Ngày hết hạn"},book_info:{modifyStatus:"Đơn sách sửa"},invoice_bonus:{course_id:"ID khoá học",days:"Số ngày"}}},comboList:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(){return comboList}),vipComboList:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(){return vipComboList}),courseList:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(){return courseList}),paymentMethodList:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(){return paymentMethodList}),saleList:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(){return saleList}),departmentList:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(){return departmentList}),currentGroup:function(){var t=this,e=this.formData.isOnline?this.groupListByCombo:this.offlineGroups;return e.filter(function(e){return t.formData.groupId.includes(e.id)})},currentFormOwner:function(){return this.currentFormUser?this.currentFormUser.course_owner:[]},currentFormEntryPoint:function(){var t,e,o=(null===(t=this.currentFormUser)||void 0===t?void 0:t.checkpoint_logs)||[],n=null===(e=this.currentGroup[0])||void 0===e?void 0:e.vip_level,i=null;return n?i=o.find(function(t){return t.lesson.get_course.name===n}):null},comboes:function t(){var e=this,t=this.formData.isVip?this.vipComboList.filter(function(t){return e.formData.isOnline?5!==t.type:5===t.type}):this.comboList,o=t.filter(function(t){return!e.formData.comboId||t.id!=e.formData.comboId.id||t.type!=e.formData.comboId.type});return o},currentCombo:function(){return this.formData.comboId},currentOwnerWatchExpired:function(){var t=this,e={N1:"N1",N2:"N2",N3:"N3",N4:"Sơ cấp N4",N5:"Sơ cấp N5",kaiwacb:"Kaiwa Sơ Cấp",kaiwasc:"Kaiwa Sơ Cấp",kaiwatc:"Kaiwa Trung Cấp",kaiwatc2:"Kaiwa Trung Cấp",kaiwanc:"Kaiwa Nâng Cao",ldn1:"Luyện đề N1",ldn2:"Luyện đề N2",ldn3:"Luyện đề N3"};return this.currentFormOwner.length&&this.currentGroup?this.currentFormOwner.filter(function(o){return t.currentGroup.map(function(o){return e[t.formData.isOnline?o.vip_level:"N".concat(6-o.level_of_n)]}).includes(o.title)}):[]},getGroupWatchExpiredStatus:function(){if(this.currentFormOwner.length&&this.currentAddCourseData){var t=_objectSpread({},this.currentAddCourseData),e={N1:"N1",N2:"N2",N3:"N3",N4:"Sơ cấp N4",N5:"Sơ cấp N5",kaiwacb:"Kaiwa Sơ Cấp",kaiwasc:"Kaiwa Sơ Cấp",kaiwatc:"Kaiwa Trung Cấp",kaiwatc2:"Kaiwa Trung Cấp",kaiwanc:"Kaiwa Nâng Cao",ldn1:"Luyện đề N1",ldn2:"Luyện đề N2",ldn3:"Luyện đề N3"};console.log("tmp course name ",t.courseName),t.courseName=e[t.courseName];var o=_.find(this.currentFormOwner,["title",t.courseName]);return o?o.watch_expired_day:null}return null},hasBook:function(){return!(!this.formData.bookIds.length||!this.formData.isOnline)},adminRole:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(){return adminRole})},watch:{filter:{handler:function(t,e){},deep:!0},"formData.comboId":function(t){if(!this.formData.id&&this.formData.comboId){var e=this.formData.comboId;e&&!this.cloneInvoice&&this.priceCalculate(),this.getGroupByCombo(t,this.search)}},"formData.department":function(t){!this.formData.id&&this.formData.comboId&&this.getGroupByCombo(this.formData.comboId,this.search)},"filter.time_by":function(t){void 0===t&&(this.filter.time_from=void 0,this.filter.time_to=void 0)},"bookData.location":function(t){this.getBookList()},"formData.email":function(t){this.emailChangeHandler(t)}},methods:{comboSelected:function(t){if(t.services&&JSON.parse(t.services)&&JSON.parse(t.services).courses){var e=JSON.parse(t.services);this.bonuses=e.courses.map(function(t){return{courseId:Number(t),days:Number(e.course_watch_expired_value)}})}},useReward:function(t){return this.formData.rewardId==t.pivot.id?(this.formData.price=this.formData.oldPrice,void(this.formData.rewardId=void 0)):this.formData.price?(this.formData.rewardId?this.formData.price=this.formData.oldPrice:this.formData.oldPrice=this.formData.price,this.formData.rewardId=t.pivot.id,"discount_money"===t.type&&(this.formData.price=this.formData.price-t.value),void("discount_percent"===t.type&&(this.formData.price=this.formData.price*(1-t.value/100)))):void alert("Không thể áp dụng voucher. Chưa điền giá")},emailChangeHandler:_.debounce(function(t){t&&this.getUserAvailableRewards(t)},1e3),getUserAvailableRewards:function(t){var e=this,o={email:t};$.get(window.location.origin+"/backend/vip/get-user-available-rewards",o,function(t){200===t.code?e.userAvailableRewards=t.data:alert("Có lỗi! Liên hệ dev!!!"),e.loading=!1})},checkDuplicate:function(t){var e=[];return t.forEach(function(o){var n=t.filter(function(t){return null!==t.invoice_id&&t.invoice_id===o.invoice_id});n.length>1&&e.push(o.user_id)}),e},isReservationCompleted:function(t){return!(!t||0===t.length)&&t.some(function(t){return"user_id_reservation"===t.target})},toggleStatus:function(t){this.filter.status=t,this.applyFilter()},getGroupStatus:function(t){switch(t){case 1:return"Đang tuyển";case 2:return"Đang học";case 3:return"Đã kết thúc";default:return"Đang tuyển"}},getGroupStatusClass:function(t){switch(t){case 1:return"badge-info";case 2:return"badge-success";case 3:return"badge-danger";default:return"Đang tuyển"}},closeAddGroupModal:function(){this.groupListByUser=[],this.groupListByCombo=[],this.showAddGroupModal=!1},openCreate:function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o=this;this.closeInvoiceDept(),this.showAddInvoiceForm=!0,this.formData.isVip=t,this.formData.isOnline=e,setTimeout(function(){$("#paidAt").datetimepicker({language:"vi"}).on("dp.change",function(t){o.onChangePaidAt(t)})},100)},limiter:function(t){t.length>2&&(console.log(" you can only select two",t),t.pop())},editInvoice:function(t){var e,o,n,i;this.editingInvoice=_.cloneDeep(t);var r=[];if(["vip_combo","offline"].includes(t.product_type)?t.vip_combo&&(r=t.vip_combo):t.combo&&(t.book&&t.combo.id===t.book.id||(r=t.combo)),t.gift){var a=JSON.parse(t.gift);this.offlineData.gift.bag=a.bag,this.offlineData.gift.book=a.book}else this.offlineData.gift.bag=0,this.offlineData.gift.book=0;if(this.formData={id:t.id,email:t.info_contact.email,password:"",fullname:t.info_contact.name,phone:t.info_contact.phone,facebook:t.info_contact.facebook,chat:t.info_contact.chat,address:t.info_contact.address||"",comboId:r,coupon:t.coupon_id,price:t.price,currency:t.currency,paymentMethod:t.payment_method_id,department:(null===(e=t.department)||void 0===e?void 0:e.id)||null,paidAt:t.paid_at,note:t.note,groupId:"",paymentStatus:t.payment_status,invoiceStatus:t.invoice_status,logs:t.logs,product_type:t.product_type,active_time:t.active_time,discountMoney:t.discount_money,paidMoney:t.paid_money,bookIds:t.books,refundBooks:t.refund_books,applied_reward:t.applied_reward},this.bonus={days:(null===(o=t.bonus)||void 0===o?void 0:o.days)||1,courseId:(null===(n=t.bonus)||void 0===n?void 0:n.course_id)||""},null!==(i=t.bonuses)&&void 0!==i&&i.length){var c;this.bonuses=null===(c=t.bonuses)||void 0===c?void 0:c.map(function(t){return{courseId:t.course_id,days:t.days}})}else this.bonuses=[this.bonus];t.book_info?this.bookData=_objectSpread({},t.book_info):this.bookData={postCode:"",address:"",price:0,payer:"dmr",totalPrice:0,location:"",status:"packing",refundFault:"",refundShip:0,modifyStatus:""},this.formData.isVip=["vip_combo","vip_course"].includes(t.product_type)||"offline"===t.product_type,this.formData.isOnline="offline"!==t.product_type,this.closeInvoiceDept(),this.showAddInvoiceForm=!0,this.getUserAvailableRewards(this.formData.email)},closeAddInvoiceForm:function(){this.groupListByCombo=[],this.showAddInvoiceForm=!1,this.resetAddInvoiceForm()},resetAddInvoiceForm:function(){this.formData={email:"",password:"",fullname:"",phone:"",facebook:"",chat:"",address:"",comboId:null,price:void 0,currency:"vnd",coupon:"",paymentMethod:void 0,department:void 0,paidAt:moment().format("YYYY-MM-DD HH:mm:ss"),note:void 0,groupId:[],isVip:!1,activeOnline:!1,discountMoney:0,paidMoney:0,bookIds:[],refundBooks:[],rewardId:void 0,oldPrice:void 0},this.bookData={postCode:"",address:"",price:0,payer:"dmr",totalPrice:0,location:"",status:"packing",refundFault:"",refundShip:0,modifyStatus:""},this.cloneInvoice=!1,this.currentAddCourseData={},this.formError={},this.offlineData.gift={bag:!1,book:!1},this.bonus={courseId:"",days:1},this.bonuses=[{courseId:"",days:1}]},changePage:function(t){var e=this;e.filter.page=t;var o=_.omit(_.pickBy(e.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]);e.getList(o),this.$router.replace(window.location.pathname+"?"+$.param(o))},applyFilter:function(){var t=this;t.filter.page=1;var e=_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page","page"]);t.getList(e),this.$router.replace(window.location.pathname+"?"+$.param(e))},onChangeCheckbox:function(t,e){var o=this;o.filter[t]=e.target.checked?1:0,o.applyFilter()},getList:function(t){var e=this;e.loading=!0,setTimeout(function(){$.get(window.location.origin+"/backend/vip/invoices",t,function(t){200==t.code?(e.results=t.data.data.map(function(t){return t}),e.filter.total_page=t.data.last_page,e.total_result=t.data.total):alert("Có lỗi! Liên hệ dev!!!"),e.loading=!1})},200),this.filterCount++},setFilterByUrl:function(){var t=this,e=$.deparam.querystring();_.forEach(e,function(e,o){t.filter[o]=e==parseInt(e)?parseInt(e):e})},resetFilter:function(){var t=this;t.filter={id:"",uuid:"",email:"",phone:"",name:"",comboId:[],courseId:void 0,groupId:void 0,paymentMethod:void 0,time_by:void 0,time_from:void 0,time_to:void 0,sort:"",sale:void 0,status:"new",isVip:void 0,voucher:void 0,admin_create:void 0,state:void 0,isAttached:void 0,isActivated:void 0,currency:void 0,refund:"",location:"",payer:"",modifyStatus:"",exportStatus:"",depositStatus:"",page:1,per_page:20,total_page:10},this.applyFilter()},onChangeDatetime:function(t){var e=this;e.filter[t.target.name]=moment(t.date).format("YYYY-MM-DD HH:mm")},onChangePaidAt:function(t){var e=this;e.formData.paidAt=moment(t.date).format("YYYY-MM-DD HH:mm")},getUser:function(t){var e=this,o={email:e.formData.email,userId:t};$.get(window.location.origin+"/backend/vip/get-user",o,function(t){200==t.code&&(e.currentFormUser=t.data)})},createInvoice:function(){var t,e=this;if(this.currentAddCourseData.isCreateUser&&!this.formData.password)return void this.$set(this.formError,"password",["Vui lòng điền mật khẩu"]);var o=_objectSpread(_objectSpread({},this.formData),{},{isVip:this.formData.isVip?1:0,isOnline:this.formData.isOnline?1:0,comboId:this.formData.comboId,comboType:null===(t=e.formData.comboId)||void 0===t?void 0:t.type,activeOnline:this.formData.activeOnline?1:0,gift:JSON.stringify(this.offlineData.gift),bookData:_objectSpread({},this.bookData),bonus:_objectSpread({},this.bonus),bonuses:this.bonuses});e.handleCreateInvoice(o)},handleCreateInvoice:function(t){var e=this;e.createInvoiceLoading=!0,$.post(window.location.origin+"/backend/vip/create-invoice",t,function(t){if(200==t.code)"new"===e.filter.status?"unpaid"===t.data.payment_status&&e.results.unshift(t.data):"completed"===e.filter.status?"paid"!==t.data.payment_status&&"completed"!==t.data.invoice_status||e.results.unshift(t.data):e.filter.status===t.data.invoice_status&&e.results.unshift(t.data),e.closeAddInvoiceForm();else if(409==t.code){var o=confirm(t.msg);if(o){var n,i=_objectSpread(_objectSpread({},e.formData),{},{isVip:e.formData.isVip?1:0,isOnline:e.formData.isOnline?1:0,comboId:e.formData.comboId,comboType:null===(n=e.formData.comboId)||void 0===n?void 0:n.type,activeOnline:e.formData.activeOnline?1:0,forceDuplicate:1,bookData:_objectSpread({},e.bookData),gift:JSON.stringify(e.offlineData.gift),bonus:_objectSpread({},e.bonus)});console.log("data",i),e.handleCreateInvoice(i)}}else"error"==t.status?e.formError=t.detail:alert("Có lỗi! Liên hệ dev");e.loading=!1}).fail(function(t){400===t.status?alert(t.responseJSON.data.msg):alert("Có lỗi! Liên hệ dev")}).always(function(){e.createInvoiceLoading=!1})},hasDuplicatesByKey:function(t,e){var o,n=new Set,i=_createForOfIteratorHelper(t);try{for(i.s();!(o=i.n()).done;){var r=o.value;if(n.has(r[e]))return!0;n.add(r[e])}}catch(a){i.e(a)}finally{i.f()}return!1},updateInvoice:function(){var t,e,o,n,i,r,a,c,s,u,d,l,f,h,p,v,m=this,g=this;if(console.log("this.formData",this.formData),console.log("this.bonus",this.bonus),console.log("this.editingInvoice",this.editingInvoice),console.log("this.courses",this.courses),this.currentAddCourseData.isCreateUser&&!this.formData.password)return void this.$set(this.formError,"password",["Vui lòng điền mật khẩu"]);if(this.hasDuplicatesByKey(this.bonuses,"courseId"))return void this.$set(this.formError,"bonuses",["Mắc gì chọn 2 khóa trùng nhau ¯\\_(ツ)_/¯"]);var y=[];if((null===(t=this.formData.bookIds)||void 0===t?void 0:t.length)!==(null===(e=this.editingInvoice.books)||void 0===e?void 0:e.length)||(null===(o=this.formData.bookIds)||void 0===o?void 0:o.reduce(function(t,e){return t+e.id},0))!==(null===(n=this.editingInvoice.books)||void 0===n?void 0:n.reduce(function(t,e){return t+e.id},0))||(null===(i=this.formData.bookIds)||void 0===i?void 0:i.reduce(function(t,e){return t+parseInt(e.quantity)},0))!==(null===(r=this.editingInvoice.books)||void 0===r?void 0:r.reduce(function(t,e){return t+parseInt(e.quantity)},0))){for(var b="",_=0;_<this.editingInvoice.books.length;_++){var I=this.editingInvoice.books[_];b+="".concat(0===_?"":", ").concat(I.name," (").concat(I.quantity,")")}for(var w="",k=0;k<this.formData.bookIds.length;k++){var D=this.formData.bookIds[k];w+="".concat(0===k?"":", ").concat(D.name," (").concat(D.quantity,")")}y.push({from:b,to:w,column:"invoice.books",target:this.editingInvoice.id,subject:"information_changed"})}if((null===(a=this.formData.refundBooks)||void 0===a?void 0:a.length)!==(null===(c=this.editingInvoice.refund_books)||void 0===c?void 0:c.length)||(null===(s=this.formData.refundBooks)||void 0===s?void 0:s.reduce(function(t,e){return t+e.id},0))!==(null===(u=this.editingInvoice.refund_books)||void 0===u?void 0:u.reduce(function(t,e){return t+e.id},0))||(null===(d=this.formData.refundBooks)||void 0===d?void 0:d.reduce(function(t,e){return t+parseInt(e.quantity)},0))!==(null===(l=this.editingInvoice.refund_books)||void 0===l?void 0:l.reduce(function(t,e){return t+parseInt(e.quantity)},0))){var L="";if(this.editingInvoice.refund_books)for(var S=0;S<this.editingInvoice.refund_books.length;S++){var C=this.editingInvoice.refund_books[S];L+="".concat(0===S?"":", ").concat(C.name," (").concat(C.quantity,")")}var N="";if(this.formData.refundBooks)for(var O=0;O<this.formData.refundBooks.length;O++){var A=this.formData.refundBooks[O];N+="".concat(0===O?"":", ").concat(A.name," (").concat(A.quantity,")")}y.push({from:L,to:N,column:"invoice.refund_books",target:this.editingInvoice.id,subject:"information_changed"})}if(this.formData.comboId&&(!["vip_combo","offline"].includes(this.editingInvoice.product_type)&&(null===(f=this.formData.comboId)||void 0===f?void 0:f.id)!==(null===(h=this.editingInvoice.combo)||void 0===h?void 0:h.id)||["vip_combo","offline"].includes(this.editingInvoice.product_type)&&(null===(p=this.formData.comboId)||void 0===p?void 0:p.id)!==(null===(v=this.editingInvoice.vip_combo)||void 0===v?void 0:v.id))){var j,x,B,P={from:(["vip_combo","offline"].includes(this.editingInvoice.product_type)?null===(j=this.editingInvoice.vip_combo)||void 0===j?void 0:j.name:null===(x=this.editingInvoice.combo)||void 0===x?void 0:x.name)||"",to:(null===(B=this.formData.comboId)||void 0===B?void 0:B.name)||"",column:"invoice.combo",target:this.editingInvoice.id,subject:"information_changed"};P.from!==P.to&&y.push(P)}JSON.stringify(this.bonuses)!==JSON.stringify(this.editingInvoice.bonuses)&&this.bonuses.length>0&&this.bonuses[0].courseId&&this.bonuses.forEach(function(t,e){
m.editingInvoice.bonuses[e]&&m.editingInvoice.bonuses[e].course_id===t.courseId&&m.editingInvoice.bonuses[e].days===t.days||y.push({from:m.editingInvoice.bonuses[e]?"".concat(m.courses.find(function(t){return t.id===m.editingInvoice.bonuses[e].course_id}).name," (").concat(m.editingInvoice.bonuses[e].days,")"):"",to:"".concat(m.courses.find(function(e){return e.id===t.courseId}).name," (").concat(t.days,")"),column:"invoice.bonus",target:m.editingInvoice.id,subject:"information_changed"})}),y.length>0&&(this.formData.editLogs=y);var G=_objectSpread(_objectSpread({},this.formData),{},{isVip:this.formData.isVip?1:0,isOnline:this.formData.isOnline?1:0,gift:JSON.stringify(this.offlineData.gift),bookData:this.bookData,bonus:_objectSpread({},g.bonus),bonuses:_toConsumableArray(g.bonuses)});$.post(window.location.origin+"/backend/vip/update-invoice",G,function(t){200==t.code?(g.getInvoice(g.formData.id),g.closeAddInvoiceForm()):"error"==t.status?g.formError=t.detail:alert("Có lỗi! Liên hệ dev"),g.loading=!1})},activeBonusCourse:function(t){var e=this;if(t.bonus){e.createInvoiceLoading=!0;var o=confirm("Bạn có chắc chắn muốn kích hoạt đơn hàng này ???");o&&$.post(window.location.origin+"/backend/vip/active-bonus/"+t.id,function(o){e.results=e.results.map(function(e){return e.id===t.id&&(e.bonus.status=0),e}),e.createInvoiceLoading=!1})}},getInvoice:function(t){var e=this;$.get(window.location.origin+"/backend/vip/invoices/"+t,function(t){200==t.code&&(e.results=e.results.map(function(e){return e.id===t.data.id&&(e=t.data),e}))})},exportExcel:function(){var t=this,e=(_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]),window.location.origin+"/backend/vip/invoices/export"+window.location.search);window.open(e)},exportBookExcel:function(){var t=this,e=(_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]),window.location.origin+"/backend/vip/book-invoice/export"+window.location.search);window.open(e),t.results=t.results.map(function(t){return t.book_info&&(t.book_info.exportStatus="print"),t}),this.bookData.exportStatus="print"},searchUser:function(t){var e=this;t.target.value;e.loading=!0,$.get(window.location.origin+"/backend/vip/invoices",filter,function(t){200==t.code?(e.results=t.data.results.map(function(t){return t}),e.filter.total_page=t.data.total_page,e.total_result=t.data.total_result):alert("Có lỗi! Liên hệ dev!!!"),e.loading=!1})},showNote:function(t){this.currentNote=t,this.showNoteModal=!0,this.closeInvoiceDept()},showLog:function(t){this.currentLog=t,this.showLogModal=!0,this.closeInvoiceDept()},createUser:function(){this.$set(this.currentAddCourseData,"isCreateUser",!0)},showAddGroup:function(t,e,o){this.currentAddCourseData={isCreateUser:!1,invoice:t,userId:e,courseName:o,search:""},this.getJoinedGroupByCourse(e,o,t.product_type),this.getUserGroupByCourse(e,o,t.id,"",t.product_type),this.getUser(e),this.closeInvoiceDept(),this.showAddGroupModal=!0},getGroupUser:function(t,e){var o;return null!==(o=_.find(e,function(e){return e.user_id===t}))&&void 0!==o?o:{}},setPoint:function(t){var e=window.prompt("Nhập điểm đầu vào");if(null!==e){var o={userId:t.user_id,groupId:t.group_id,point:e};$.post(window.location.origin+"/backend/vip/set-entry-point",o,function(o){200==o.code?t.entry_point=e:alert("Có lỗi! Liên hệ bạn Quang 18 tủi"),vm.loading=!1})}},searchGroup:function(){this.getUserGroupByCourse(this.currentAddCourseData.userId,this.currentAddCourseData.courseName,this.currentAddCourseData.invoice.id,this.currentAddCourseData.search,this.currentAddCourseData.invoice.product_type,this.currentAddCourseData.byPassGroupLevel?1:0)},searchGroupByCourse:function(){this.getGroupByCombo(this.formData.comboId,this.search)},getJoinedGroupByCourse:function(t,e,o){var n=this,i={userId:t,courseName:e,isOffline:"offline"===this.currentAddCourseData.invoice.product_type?1:0,type:o};$.get(window.location.origin+"/backend/vip/get-joined-group-by-course",i,function(t){200==t.code?n.joinedGroupListByUser=t.data:alert("Có lỗi! Liên hệ dev!!!"),n.loading=!1})},getUserGroupByCourse:function(t,e,o){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=arguments.length>4?arguments[4]:void 0,r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,a=this,c={userId:t,courseName:e,invoiceId:o,search:n,type:i,byPassGroupLevel:r};$.get(window.location.origin+"/backend/vip/get-user-group-by-course",c,function(t){200==t.code?a.groupListByUser=t.data:alert("Có lỗi! Liên hệ dev!!!"),a.loading=!1})},getGroupByCombo:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(t){var o=this,n={comboId:t.id,departmentId:this.formData.department,search:e,isOnline:this.formData.isOnline?1:0,isVip:this.formData.isVip?1:0,byPassGroupLevel:this.byPassGroupLevel?1:0};$.get(window.location.origin+"/backend/vip/get-group-by-courses",n,function(t){200==t.code?o.formData.isOnline?o.groupListByCombo=t.data:o.offlineGroups=t.data:alert("Có lỗi! Liên hệ dev!!!"),o.loading=!1})}},kick:function(t,e){var o=this,n={userId:t,groupId:e,invoiceId:this.currentAddCourseData.invoice.id};$.post(window.location.origin+"/backend/vip/kick-user",n,function(t){200==t.code?(o.joinedGroupListByUser=o.joinedGroupListByUser.filter(function(t){return t.id!==e}),o.groupListByUser.unshift(t.data),o.getInvoice(o.currentAddCourseData.invoice.id)):alert("Có lỗi! Liên hệ bạn Quang 18 tủi"),o.loading=!1})},addUser:function(t,e,o,n){var i=this;return _asyncToGenerator(_regeneratorRuntime().mark(function r(){var a,c,s;return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(a=i,!a.addUserLoading){r.next=3;break}return r.abrupt("return");case 3:if(a.addUserLoading=!0,c={invoiceId:t,userId:e,groupId:o,activeOnline:n?1:0},!i.getGroupWatchExpiredStatus||!n){r.next=12;break}if(s=confirm("Học viên đang có khoá online còn hạn đến "+i.getGroupWatchExpiredStatus+". Xác nhận kích hoạt?"),!s){r.next=10;break}return r.next=10,i.handleAddUser(c);case 10:r.next=14;break;case 12:return r.next=14,i.handleAddUser(c);case 14:return r.next=16,i.getInvoice(t);case 16:case"end":return r.stop()}},r)}))()},copyLinkChat:function(t){if(null==t||"null"==t)return void alert("Nhóm chưa tạo group chat");var e=new URL(window.location.origin),o=e.searchParams;o.append("chat",t),e.search=o.toString(),navigator.clipboard.writeText(e.href).then(function(t){alert("Đã copy thành công!")})},handleAddUser:function(t){var e=this;$.post(window.location.origin+"/backend/vip/add-user",t,function(o){200==o.code&&(e.groupListByUser=e.groupListByUser.filter(function(e){return e.id!==t.groupId}),e.joinedGroupListByUser.unshift(o.data),"new"===e.filter.status&&(e.results=e.results.filter(function(e){return e.id!==t.invoiceId}))),e.addUserLoading=!1}).fail(function(t){400===t.status?alert(t.responseJSON.data.msg):alert("Có lỗi! Liên hệ dev")})},toggleAddUser:function(t){this.formData.groupId.includes(t)?this.formData.groupId=this.formData.groupId.filter(function(e){return e!=t}):this.formData.groupId.push(t)},getLevelStatus:function(t,e){var o;return t.user?"offline"===t.product_type?_.findIndex(null===(o=t.user)||void 0===o||null===(o=o.offline_user)||void 0===o?void 0:o.courses,["level_of_n",6-e[1]]):_.findIndex(t.user.groups,["vip_level",e]):-1},activeInvoice:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"course",o=this;o.activeInvoiceLoading=!0;var n=confirm("Bạn có chắc chắn muốn kích hoạt đơn hàng này ???");1==n&&$.ajax({type:"post",url:"/backend/invoice/do-active",data:{_token:$("input[name=_token]").val(),id:t,type:e},success:function(e){"success"==e?("new"===o.filter.status&&(o.results=o.results.filter(function(e){return e.id!==t})),"completed"===o.filter.status&&(o.results=o.results.map(function(e){return e.id===t&&(e.invoice_status="completed"),e}))):alert("Kích hoạt lỗi")}}).done(function(){o.activeInvoiceLoading=!1})},initConversation:function(t){$.ajax({type:"post",url:"/backend/user/create-conversation",data:{id:t},success:function(t){window.open(window.location.origin+"/backend/chat#"+t,"_blank")}})},getColumn:function(t){return _.get(this.columns,t)},getLogFrom:function(t){if("invoice.department_id"==t.column){var e,o;return null!==(e=null===(o=this.departmentList.find(function(e){return e.id==t.from}))||void 0===o?void 0:o.name)&&void 0!==e?e:t.from}if("invoice.payment_method_id"==t.column){var n,i;return null!==(n=null===(i=this.paymentMethodList.find(function(e){return e.id==t.from}))||void 0===i?void 0:i.name)&&void 0!==n?n:t.from}return t.from?t.from:"null"},getLogTo:function(t){if("invoice.department_id"==t.column){var e,o;return null!==(e=null===(o=this.departmentList.find(function(e){return e.id==t.to}))||void 0===o?void 0:o.name)&&void 0!==e?e:t.to}if("invoice.payment_method_id"==t.column){var n,i;return null!==(n=null===(i=this.paymentMethodList.find(function(e){return e.id==t.to}))||void 0===i?void 0:i.name)&&void 0!==n?n:t.to}return t.to?t.to:"null"},cancelInvoice:function(t){var e=this,o=confirm("Bạn có chắc chắn muốn hủy đơn hàng này ?");1==o&&$.ajax({type:"post",url:"/backend/invoice/do-cancel",data:{_token:$("input[name=_token]").val(),id:t},success:function(o){"success"==o?"cancel"!==e.filter.status&&(e.results=e.results.filter(function(e){return e.id!==t})):alert("Không hủy được")}})},deleteInvoice:function(t){var e=this,o=confirm("Bạn có chắc chắn muốn hủy đơn hàng này ?");1==o&&$.ajax({type:"delete",url:"/backend/invoice/"+t,data:{_token:$("input[name=_token]").val()},success:function(o){"success"==o?e.results=e.results.filter(function(e){return e.id!==t}):alert("Không hủy được")}})},activeOnline:function(t,e,o){var n=this;n.activeLoading=!0;var i={userId:t,groupId:e,invoiceId:o};$.ajax({type:"post",url:"/backend/vip/active-online",data:_objectSpread({_token:$("input[name=_token]").val()},i),success:function(t){"success"==t&&alert("Đã kích hoạt"),n.activeLoading=!1}}).done(function(){n.activeLoading=!1})},getGroupUserLength:function(t){var e=this.reservations.findIndex(function(e){return e.id===t.id});return e===-1?t.group_user.length:this.reservations[e].count},getCurrencySymbol:function(t){return"vnd"===t?"đ":"¥"},invoiceDetail:function(t){var e=this,o=parseInt(t.discount_money),n=parseInt(t.paid_money),i=t.price-(o+n);e.printInvoice={id:t.id,email:t.info_contact.email,fullname:t.info_contact.name,price:t.price,currency:t.currency,phone:t.info_contact.phone,product_type:t.product_type,product_name:t.product_name,discountMoney:t.discount_money,paidMoney:t.paid_money,createdAt:t.created_at,note:t.note,paidMoneyText:to_vietnamese(t.paid_money),oweMoney:i,gift:t.gift?JSON.parse(t.gift):e.offlineData.gift,adminCreate:t.admin_active_name},e.showInvoiceDetail=!0},closeInvoiceDetail:function(){this.printInvoice={},this.showInvoiceDetail=!1},closeInvoiceDept:function(){this.depositInvoice={},this.showInvoiceDept=!1},downloadInvoice:function(){var t=this,e=document.getElementById("print-invoice"),o=window.jspdf.jsPDF,n=new o("p","mm","a4");html2canvas(e,{x:0,y:5,witdth:250,windowWidth:1e3,scale:1}).then(function(e){var o=e.toDataURL();n.addImage(o,"PNG",10,10),n.autoPrint({variant:"non-conform"}),n.save("invoice"+t.printInvoice.id+".pdf")})},makeInvoicePaidMore:function(t){var e;this.cloneInvoice=!0;var o=[],n=parseInt(t.price),i=parseInt(t.discount_money),r=parseInt(t.paid_money),a=n-(i+r);["vip_combo","offline"].includes(t.product_type)?t.vip_combo&&(o=t.vip_combo):t.combo&&(t.book&&t.combo.id===t.book.id||(o=t.combo)),this.formData={email:t.info_contact.email,password:"",fullname:t.info_contact.name,phone:t.info_contact.phone,facebook:t.info_contact.facebook,chat:t.info_contact.chat,address:t.info_contact.address,comboId:o,coupon:t.coupon_id,price:a,currency:t.currency,department:(null===(e=t.department)||void 0===e?void 0:e.id)||null,paymentMethod:t.payment_method_id,paidAt:t.paid_at,note:t.note,discountMoney:0,paidMoney:"",groupId:[],bookIds:[],refundBooks:[],paidFor:t.paid_for_id?t.paid_for_id:t.id},this.offlineData.gift=JSON.parse(t.gift),this.openCreate(["vip_combo","vip_course"].includes(t.product_type)||"offline"===t.product_type,"offline"!==t.product_type)},checkPaidMore:function(t){var e=parseInt(t.discount_money),o=parseInt(t.paid_money),n=t.price-(e+o);return t.price>=e+o&&0!==n},checkDidAdd:function(t){var e=this.joinedList.find(function(e){return e.type===t.type&&e.vip_level===t.vip_level});return!e},priceCalculate:function(){var t=0;this.formData.bookIds.length?(this.formData.bookIds.forEach(function(e){var o=e.quantity*e.price;t+=o}),this.bookData.totalPrice=t,this.formData.comboId&&!Array.isArray(this.formData.comboId)&&(t+=parseInt(this.formData.comboId.price)),this.formData.price=t||void 0):this.formData.comboId?this.formData.price=this.formData.comboId.price||void 0:this.formData.price=void 0},changeBookStatus:function(){this.formData.refundBooks.length&&this.formData.id&&(this.bookData.status="return")},getBookList:function(){var t=this;$.get(window.location.origin+"/backend/book-inventory/get-list",t.bookData,function(e){200==e.status&&(t.bookList=e.data.map(function(t){return _objectSpread(_objectSpread({},t),{},{inventory:(parseInt(t.totalIn)||0)-(parseInt(t.totalOut)||0),quantity:1})}),t.formData.bookIds.forEach(function(e,o){var n=_.find(t.bookList,{id:e.id}),i=_objectSpread({},e);i.inventory=n.inventory,n&&t.$set(t.formData.bookIds,o,i)}))})},getCourses:function(){var t=this;$.get(window.location.origin+"/backend/courses",function(e){200==e.code&&(t.courses=e.data)})},getCourseFromService:function(t){return this.validCourses.find(function(e){return Number(e.id)===Number(t)})}},mounted:function(){var t=this;t.setFilterByUrl();var e=_.omit(_.pickBy(t.filter,function(t,e){return void 0!==t&&null!==t&&""!==t}),["total_page"]);this.getList(e),this.getBookList(),Pusher.logToConsole=!1;var o=new Pusher("7d39d4954f600d3bb86c",{cluster:"ap3"}),n=o.subscribe("invoice");n.bind("reservation",function(e){var o=t.reservations.findIndex(function(t){return t.id===e.id});o===-1?t.reservations.push(e):t.$set(t.reservations,o,e)}),this.getCourses()}});