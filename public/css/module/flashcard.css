@charset "UTF-8";
.opacity-0-important {
  opacity: 0 !important;
}

.text-result p span, .text-result {
  font-size: 24px !important;
}

.pulse-container-flashcard {
  position: absolute;
  top: -40px;
  width: 100%;
  height: -webkit-fill-available;
  overflow: hidden;
}

.pulse-circle-flashcard {
  top: -130px;
  left: 25%;
  width: 52%;
  height: 272px;
  border-radius: 50%;
  position: absolute;
  -webkit-transition: -webkit-transform 0.1s ease-in-out;
  transition: -webkit-transform 0.1s ease-in-out;
  transition: transform 0.1s ease-in-out;
  transition: transform 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
  -webkit-filter: blur(35px);
          filter: blur(35px);
}

.card-wrap::after {
  content: "";
  position: fixed;
  bottom: 71px;
  left: 0;
  right: 0;
  height: 30px;
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(white));
  background: -webkit-linear-gradient(top, transparent, white);
  background: linear-gradient(to bottom, transparent, white);
  pointer-events: none;
  -webkit-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  width: calc(100% - 2rem);
  margin-left: 1rem;
}

.card-wrap-back::after {
  content: "";
  position: fixed;
  bottom: 224px;
  left: 0;
  right: 0;
  height: 30px;
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(white));
  background: -webkit-linear-gradient(top, transparent, white);
  background: linear-gradient(to bottom, transparent, white);
  pointer-events: none;
  -webkit-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  width: calc(100% - 2rem);
  margin-left: 1rem;
}

/* Class để ẩn phần mờ */
.card-wrap.fade-hidden::after {
  opacity: 0;
}

.card-wrap-back::-webkit-scrollbar, .card-wrap::-webkit-scrollbar {
  width: 6px;
  border-radius: 10px;
}

.card-wrap-back::-webkit-scrollbar-track, .card-wrap::-webkit-scrollbar-track {
  border-radius: 6px;
}

.card-wrap-back::-webkit-scrollbar-thumb, .card-wrap::-webkit-scrollbar-thumb {
  background: #D9D9D9;
  border-radius: 10px;
}

.card-wrap-back::-webkit-scrollbar-thumb:hover, .card-wrap::-webkit-scrollbar-thumb:hover {
  background: #8e8d8d;
}

.language-switch, .shuffle-switch {
  position: relative;
  display: inline-block;
  width: 64px;
  height: 32px;
}

.language-switch input, .shuffle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.language-slider, .shuffle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 8px;
}

.language-slider {
  background-color: #CCF8D1;
}

.shuffle-slider {
  background-color: #B3B3B3;
}

.language-slider .slider-text {
  font-family: Beanbag_Dungmori_Rounded_Medium;
  color: #757575;
  font-size: 12px;
  font-weight: bold;
  position: absolute;
  right: 8px;
}

.language-slider .flag {
  position: absolute;
  height: 26px;
  width: 26px;
  left: -6px;
  bottom: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
}

.flag.vi {
  background-image: url("/path-to-your-vietnam-flag.png");
  background: #FF0000;
  position: relative;
}

.flag.vi::after {
  content: "★";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  color: #FFFF00;
  font-size: 14px;
}

.flag.ja {
  background-image: url("/path-to-your-japan-flag.png"); /* Thay đổi đường dẫn tới ảnh cờ Nhật */
  /* Hoặc sử dụng CSS để vẽ cờ */
  background: #FFFFFF;
  position: relative;
}

.flag.ja::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: #FF0000;
  border-radius: 50%;
}

input:checked + .language-slider, .shuffle-switch input:checked + .shuffle-slider {
  background-color: #CCF8D1;
}

input:checked + .language-slider .flag {
  -webkit-transform: translateX(32px);
      -ms-transform: translateX(32px);
          transform: translateX(32px);
}

input:checked + .language-slider .slider-text {
  left: 8px;
  right: auto;
}

.language-slider.round, .shuffle-slider.round {
  border-radius: 34px;
}

/* Thêm hiệu ứng hover */
.language-switch:hover .language-slider, .shuffle-switch:hover .shuffle-slider {
  opacity: 0.9;
}

.shuffle-slider .slider-circle {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 3px;
  background-color: #D9D9D9;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border-radius: 50%;
}

.shuffle-switch input:checked + .shuffle-slider .slider-circle {
  -webkit-transform: translateX(30px);
      -ms-transform: translateX(30px);
          transform: translateX(30px);
  background-color: #07403F;
}

/* Popover styles */
.popover_setting_content {
  margin-top: 10px;
}

.popover_setting_content::before {
  content: "";
  position: absolute;
  margin: 15px;
  top: -24px;
  right: 30px;
  width: 16px;
  height: 16px;
  background-color: white;
  -webkit-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
          transform: rotate(45deg);
  -webkit-box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
          box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
}

.popover_setting_content > div {
  background-color: white;
  position: relative;
  z-index: 1;
}

.popover_setting_content_item {
  -webkit-transition: background-color 0.2s ease;
  transition: background-color 0.2s ease;
}

.flashcards-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-flow: column;
      -ms-flex-flow: column;
          flex-flow: column;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  /* elements on stacked cards */
}
.flashcards-wrap .a_cursor--pointer {
  cursor: pointer;
}
.flashcards-wrap .content {
  width: 80%;
}
.flashcards-wrap .no-transition {
  -webkit-transition: none !important;
  transition: none !important;
}
.flashcards-wrap .stackedcards.init {
  opacity: 0;
  /* set về 0 để tạo hiệu ứng hiện ra từ hư vô thật ma mị */
}
.flashcards-wrap .stackedcards {
  position: relative;
}
.flashcards-wrap .stackedcards * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.flashcards-wrap .stackedcards--animatable {
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.flashcards-wrap .stackedcards .stackedcards-container > *,
.flashcards-wrap .stackedcards-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  will-change: transform, opacity;
  top: 0;
  border-radius: 10px 10px 0 0;
}
.flashcards-wrap .stackedcards-overlay.left > div,
.flashcards-wrap .stackedcards-overlay.right > div,
.flashcards-wrap .stackedcards-overlay.top > div {
  width: 100%;
  height: 100%;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.flashcards-wrap .stackedcards-overlay.left,
.flashcards-wrap .stackedcards-overlay.right,
.flashcards-wrap .stackedcards-overlay.top {
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
          justify-content: center;
  left: 0;
  opacity: 0;
  top: -44px;
  height: 100%;
  font-size: 24px;
  font-weight: 700;
}
.flashcards-wrap .stackedcards-overlay.top {
  background: transparent;
  color: transparent;
}
.flashcards-wrap .stackedcards-overlay.right {
  height: 50px;
  background: #CEFFD8;
}
.flashcards-wrap .stackedcards-overlay.left {
  height: 50px;
  background: #FFF193;
}
.flashcards-wrap .stackedcards-overlay.left:empty,
.flashcards-wrap .stackedcards-overlay.right:empty,
.flashcards-wrap .stackedcards-overlay.top:empty {
  display: none !important;
}
.flashcards-wrap .stackedcards-overlay-hidden {
  display: none;
}
.flashcards-wrap .stackedcards-origin-top {
  -webkit-transform-origin: top;
  -ms-transform-origin: top;
  transform-origin: top;
}
.flashcards-wrap .stackedcards-top {
  background: transparent;
  height: 100%;
}
.flashcards-wrap .stackedcards .stackedcards-container > :nth-child(1) {
  position: relative;
  display: block;
}
.flashcards-wrap .card-item {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 100%;
}
.flashcards-wrap .card-item .card-inner.flip {
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
.flashcards-wrap .card-item .card-inner {
  position: relative;
  -webkit-transition: -webkit-transform 0.6s;
  transition: -webkit-transform 0.6s;
  transition: transform 0.6s;
  transition: transform 0.6s, -webkit-transform 0.6s;
  width: 100%;
  min-height: 550px;
  transform-style: preserve-3d;
  -webkit-transform-style: preserve-3d;
}
.flashcards-wrap .card-item .card-inner .card__face {
  padding: 20px 20px 20px 20px;
  -webkit-box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.3);
  border-radius: 32px;
  position: absolute;
  height: 100%;
  width: 100%;
  backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  background-color: #FFF;
}
.flashcards-wrap .card-item .card-inner .card__face .card-inner.flip {
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
.flashcards-wrap .card-item .card-inner .card__face--back {
  -webkit-transform: rotateY(180deg);
  transform: rotateY(180deg);
}

.left-action, .right-action {
  -webkit-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}

.left-action:hover, .left-action:active, .left-action.active {
  background-color: #FFE271;
}

.right-action:hover, .right-action:active, .right-action.active {
  background-color: #98FFAE;
}
