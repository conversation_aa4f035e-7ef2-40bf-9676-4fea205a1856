<table>
    <thead>
    <tr>
        <td>Họ tên</td>
        <td>Email</td>
        <td>SĐT</td>
        <td>FB</td>
        <td>Tr<PERSON><PERSON> độ</td>
        <td>Nợ học phí</td>
    </tr>
    </thead>
    <tbody>
        @foreach ($students as $student)
            <tr>
                <td>
                    <div>{{ $student['last_name'] }} {{ $student['first_name'] }}</div>
                    @if($student['user_dmr'] != null && isset($student['is_debt']) && $student['is_debt'] == 1)
                        <div>{{ $student['user_dmr']['id'] }} - {{ $student['user_dmr']['email'] }}</div>
                    @endif
                    
                </td>
                <td>{{ $student['email'] ?? '--' }}</td>
                <td>{{ $student['phone'] ?? '--' }}</td>
                <td>
                    @if(isset($student['facebook_url']) && $student['facebook_url'])
                        <a href="{{ $student['facebook_url'] }}" target="_blank">FB</a>
                    @else
                        --
                    @endif
                </td>
                <td>{{ $student['course_owner'] ?? '--' }}</td>
                <td>
                    <div>{{ isset($student['is_debt']) && $student['is_debt'] == 1 ? 'Đang nợ' : '' }}</div>
                    @if (isset($student['is_debt']) && $student['is_debt'] == 1)
                        <div>{{ $student['debt_amount'] }}</div>
                    @endif
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
