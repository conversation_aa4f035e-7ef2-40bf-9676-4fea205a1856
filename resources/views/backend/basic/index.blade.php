@extends('backend._default.dashboard')

@section('description') Quản lý bình luận @stop
@section('keywords') comment @stop
@section('author') dungmori.com @stop
@section('title') Admin | Quản lý bình luận @stop
@section('assets')
	<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
@stop

@section('content')
	<div id="basicRatio">
		<div class="flex flex-wrap gap-2">
			<el-select v-model="courseId" placeholder="Khóa" @change="fetchData()">
				<el-option
						v-for="item in courses"
						:key="item.value"
						:label="item.label"
						:value="item.value"
				>
				</el-option>
			</el-select>
			<el-select v-model="type" placeholder="Loại" @change="fetchData()">
				<el-option
						v-for="item in types"
						:key="item.value"
						:label="item.label"
						:value="item.value"
				>
				</el-option>
			</el-select>
			<div class="block">
				<el-date-picker
						v-model="dateRange"
						type="daterange"
						range-separator="To"
						start-placeholder="Từ ngày"
						end-placeholder="Đến ngày"
						@change="fetchData()"
				>
				</el-date-picker>
			</div>
			<el-button @click="copyText">Sao chép bảng</el-button>
		</div>
		<div class="mt-5">
			<div v-if="total">% vào học 2 tuần đầu: @{{ (tableData.filter(item => item.isFirstTwoWeekLearn).length * 100 / total).toFixed(2) }}% (@{{ tableData.filter(item => item.isFirstTwoWeekLearn).length }} / @{{ total }})</div>
			<el-table
					id="basicTable"
					class="mt-3"
					:data="tableData"
					style="width: 100%;"
					v-loading="loading"
					:summary-method="getSummaries"
					show-summary
					height="700"
					border
					fit
			>
				<el-table-column
						label="STT"
						type="index"
						width="100"
				>
				</el-table-column>
				<el-table-column
						prop="user"
						label="Tên"
				>
					<template slot-scope="scope">
						<div>@{{ scope.row.user?.name }}</div>
					</template>
				</el-table-column>
				<el-table-column
						prop="user"
						label="Email"
				>
					<template slot-scope="scope">
						@{{ scope.row.user?.email }}
					</template>
				</el-table-column>
				<el-table-column
						prop="firstTwoWeekPercent"
						:label="`% 2 tuần đầu`">
					<template slot-scope="scope">
						<span @click="console.log(scope.row.firstTwoWeekProgresses.map(item => item.lesson_id))">@{{ scope.row.firstTwoWeekPercent }}% (@{{ scope.row.firstTwoWeek }} / @{{ lessons.length }})</span>
					</template>
				</el-table-column>
				<el-table-column
						prop="firstMonthPercent"
						label="% tháng đầu">
					<template slot-scope="scope">
						<span @click="console.log(scope.row.firstMonthProgresses.map(item => item.lesson_id))">@{{ scope.row.firstMonthPercent }}% (@{{ scope.row.firstMonth }} / @{{ lessons.length }})</span>
					</template>
				</el-table-column>
				<el-table-column
						prop="firstMonthExamAvg"
						label="Điểm trung bình tháng đầu">
					<template slot-scope="scope">
						@{{ scope.row.firstMonthExam ? scope.row.firstMonthExamAvg.toFixed(2) : 0 }}
					</template>
				</el-table-column>
				<el-table-column
						prop="passedResultPercent"
						label="% bài test đạt">
					<template slot-scope="scope">
						<span @click="console.log(scope.row.passedResults.map(item => item.lesson_id), scope.row.results)">@{{ scope.row.passedResultPercent }}% (@{{ scope.row.passedResults.length }} / @{{ scope.row.results.length }})</span>
					</template>
				</el-table-column>
				<el-table-column
						prop="firstMonthExam"
						label="Số bài test tháng đầu">
					<template slot-scope="scope">
						@{{ scope.row.results.length }}
					</template>
				</el-table-column>
				<el-table-column
						prop="active_time"
						label="Ngày kích hoạt">
				</el-table-column>
				<el-table-column
						prop="firstLearn"
						label="Ngày bắt đầu học">
					<template slot-scope="scope">
						@{{ scope.row.firstLearn ? scope.row.firstLearn.created_at : '--' }}
					</template>
				</el-table-column>
			</el-table>
		</div>
	</div>

@stop

@section('footer')
	<script type="text/javascript">
{{--		var comments = {!! json_encode($comments) !!};--}}
	</script>
	<script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
	<script src="{{ asset('plugin/vue/vue.min.js') }}"></script>
	<script src="{{ asset('plugin/element-ui/element-ui.min.js') }}" type="text/javascript"></script>
	<script src="//unpkg.com/element-ui/lib/umd/locale/vi.js"></script>
	<script>
		ELEMENT.locale(ELEMENT.lang.vi)
	</script>
	<script src="{{ asset('plugin/moment/moment.js') }}"></script>
	<script src="{{ asset('/plugin/jquery/axios.min.js') }}"></script>
	<script src="{{asset('plugin/lodash/lodash.js')}}"></script>
	<script>
		var app = new Vue({
			el: '#basicRatio',
			data: {
				loading: false,
				courseId: 17,
				from: '',
				to: '',
				dateRange: null,
				type: 'online_basic',
				types: [
					{
						value: 'offline',
						label: 'Offline'
					},
					{
						value: 'online_basic',
						label: 'Online Basic'
					},
					{
						value: 'vip',
						label: 'VIP'
					},
				],
				courses: [
					{
						value: 17,
						label: 'N1'
					}, {
						value: 16,
						label: 'N2'
					}, {
						value: 3,
						label: 'N3'
					}, {
						value: 40,
						label: 'N4'
					}, {
						value: 39,
						label: 'N5'
					},
					{
						value: 44,
						label: 'N3 mới'
					},
					{
						value: 45,
						label: 'N2 mới'
					},
					{
						value: 46,
						label: 'N1 mới'
					},
				],
				invoices: [],
				twoFirstWeekLearned: 0,
				lessonIds: [],
				requireLessonIds: [],
				lessons: [],
				total: 0,
				page: 1,
				perPage: 5,
				progresses: [],
				trackingStats: [],
				lessonResults: [],
				examResults: [],
			},
			computed: {
				tableData() {
					return this.invoices.map(invoice => {
						const activeTime = moment(invoice.active_time).startOf('day');
						invoice.progresses = this.progresses.filter(item => item.user_id === invoice.user_id && moment(item.created_at).isBetween(activeTime, activeTime.clone().add(1, 'month')))
						invoice.trackingStats = this.trackingStats.filter(item => item.user_id === invoice.user_id && moment(item.created_at).isBetween(activeTime, activeTime.clone().add(1, 'month')))
						invoice.lessonResults = this.lessonResults.filter(item => item.user_id === invoice.user_id && moment(item.created_at).isBetween(activeTime, activeTime.clone().add(1, 'month')))
						invoice.examResults = this.examResults.filter(item => item.user_id === invoice.user_id && moment(item.created_at).isBetween(activeTime, activeTime.clone().add(1, 'month')))
						invoice.results = []
						// Init fields
						invoice.firstTwoWeek = 0;
						invoice.firstTwoWeekPercent = 0;
						invoice.firstMonth = 0;
						invoice.firstMonthPercent = 0;
						invoice.firstMonthExam = 0;
						invoice.firstMonthExamAvg = 0;
						invoice.firstLearn = null;
						invoice.passedResults = [];
						invoice.passedResultPercent = 0;
						invoice.isFirstTwoWeekLearn = false;
						invoice.firstMonthProgresses = [];
						invoice.firstTwoWeekProgresses = [];

						if (invoice.trackingStats && invoice.trackingStats.length > 0) {
							invoice.isFirstTwoWeekLearn = invoice.trackingStats.filter(item => {
								return moment(item.created_at).isBetween(activeTime, activeTime.clone().add(14, 'days'));
							}).length > 0;
						}

						// 1. First learn tracking
						if (invoice.trackingStats.length > 0) {
							invoice.firstLearn = invoice.trackingStats.reduce((earliest, current) => {
								return new Date(current.created_at) < new Date(earliest.created_at) ? current : earliest;
							}, invoice.trackingStats[0]);
						}

						// 3. Lesson progress
						const twoWeekProgress = invoice.progresses.filter(item =>
								moment(item.created_at).isBetween(activeTime, activeTime.clone().add(14, 'days'))
						);
						const oneMonthProgress = invoice.progresses;

						oneMonthProgress.forEach(progress => {
							const lesson = this.lessons.find(l => l.id === progress.lesson_id);
							if (!lesson) return;

							const isComplete = lesson.exercise
									? progress.example_progress >= 85
									: progress.video_progress >= 85;

							if (isComplete) {
								invoice.firstMonth++;
								invoice.firstMonthProgresses.push(progress);
							}
						});

						twoWeekProgress.forEach(progress => {
							const lesson = this.lessons.find(l => l.id === progress.lesson_id);
							if (!lesson) return;

							const isComplete = lesson.exercise
									? progress.example_progress >= 85
									: progress.video_progress >= 85;

							if (isComplete) {
								invoice.firstTwoWeek++;
								invoice.firstTwoWeekProgresses.push(progress);
							}
						});

						invoice.firstTwoWeekPercent = (invoice.firstTwoWeek * 100 / this.lessons.length).toFixed(2);
						invoice.firstMonthPercent = (invoice.firstMonth * 100 / this.lessons.length).toFixed(2);
						// 4. Exam/test results
						const isBasic = ![17, 16, 3].includes(this.courseId);
						let results = isBasic ? invoice.examResults || [] : invoice.lessonResults || [];

						const highestPerLesson = Object.values(results.reduce((acc, curr) => {
							const key = curr.lesson_id;
							const scoreField = isBasic ? 'total_score' : 'total_grade';
							if (!acc[key] || acc[key][scoreField] < curr[scoreField]) {
								acc[key] = curr;
							}
							return acc;
						}, {}));
						

						invoice.passedResults = _.uniqBy(results.filter(result => isBasic ? result.is_passed : result.passed), 'lesson_id');

						invoice.firstMonthExam = highestPerLesson.length;
						invoice.firstMonthExamAvg =
								highestPerLesson.length ?
								highestPerLesson.reduce((sum, item) => {
									return sum + (isBasic ? item.total_score : item.total_grade);
								}, 0) / highestPerLesson.length : 0;

						results = _.uniqBy(results, 'lesson_id')
						invoice.passedResultPercent = (results.length ? invoice.passedResults.length * 100 / results.length : 0).toFixed(2)

						invoice.results = results;
						return invoice;
					});
				}
			},
			mounted() {
				this.getData();
			},
			methods: {
				copyText() {
					var el = document.getElementById("basicTable");

					var body = document.body,
							range,
							sel;

					if (document.createRange && window.getSelection) {
						range = document.createRange();

						sel = window.getSelection();

						sel.removeAllRanges();

						try {
							range.selectNodeContents(el);

							sel.addRange(range);
						} catch (e) {
							range.selectNode(el);

							sel.addRange(range);
						}
					} else if (body.createTextRange) {
						range = body.createTextRange();

						range.moveToElementText(el);

						range.select();
					}

					document.execCommand("Copy");
				},
				getSummaries(param) {
					const { columns, data } = param;
					const sums = [];

					columns.forEach((column, index) => {
						if (index === 0) {
							sums[index] = 'Trung bình';
							return;
						}

						const values = data.map(item => Number(item[column.property])).filter(val => !isNaN(val));
						if (values.length) {
							const average = values.reduce((sum, val) => sum + val, 0) / values.length;
							sums[index] = average.toFixed(2); // or use Math.round if preferred
						} else {
							sums[index] = '--';
						}
					});

					return sums;
				},
				handlePageChange(page) {
					this.page = page
					this.getData();
				},
				fetchData() {
					this.getData();
				},
				async getData() {
					const that = this;
					that.loading = true;

					const from = this.dateRange ? moment(this.dateRange[0]).format('YYYY-MM-DD') : '';
					const to = this.dateRange ? moment(this.dateRange[1]).format('YYYY-MM-DD') : '';

					try {
						const response = await axios.get(`/backend/ti-le-hoc-sau-mua/get-data?type=${this.type}&courseId=${this.courseId}&from=${from}&to=${to}&page=${this.page}&perPage=${this.perPage}`);

						this.lessons = response.data.courseLessons || [];

						this.total = response.data.invoices.length;
						this.invoices = response.data.invoices || [];
						this.progresses = response.data.progresses || [];
						this.trackingStats = response.data.trackingStats || [];
						this.lessonResults = response.data.lessonResults || [];
						this.examResults = response.data.examResults || [];
					} catch (error) {
						console.error(error);
					} finally {
						this.loading = false;
					}
				}
			}
		});
	</script>
@stop
