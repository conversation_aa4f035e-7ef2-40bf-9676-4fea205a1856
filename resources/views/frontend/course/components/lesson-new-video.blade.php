<div>
    @if ($isLocked)
        <div class="video relative w-full min-h-[500px] sp:min-h-[200px] aspect-video bg-cover bg-center" id="video-wrapper"
             style="@if ($lesson->avatar_name) background-image: url({{ url('cdn/lesson/default/' . $lesson->avatar_name) }}) @endif">
            <div class="absolute top-0 w-full h-full bg-[#07403FCC] font-beanbag flex items-center justify-center">
                <div>
                    <div class="flex items-center">
                        <img class="w-[18px] h-[18px]" src="{{ asset('images/icons/white-lock.png') }}" alt="lock.png">
                        <span class="text-xl sp:text-base text-white ml-2">
                            {{ !auth()->check() ? 'Bạn cần đăng nhập để học hoặc bình luận' : '<PERSON><PERSON> kh<PERSON><PERSON> học để học ngay ' }}
                        </span>
                    </div>
                    <div class="flex items-center justify-center mt-10">
                        @if (!auth()->check())
                            <a data-fancybox data-animation-duration="300" data-src="#auth-container"
                               style="margin-right: 10px;">
                                <div class="bg-[#57D061] mx-5 sp:mx-1 text-xl sp:text-base !text-[#07403F] border border-white rounded-full py-2 w-[170px] text-center uppercase cursor-pointer"
                                     onclick="swichTab('login')">Đăng nhập
                                </div>
                            </a>
                            <a data-fancybox data-animation-duration="300" data-src="#auth-container">
                                <div class="bg-[#EC6E23] mx-5 sp:mx-1 text-xl sp:text-base !text-white border border-white rounded-full py-2 w-[170px] text-center uppercase cursor-pointer"
                                     onclick="swichTab('register')">Đăng ký
                                </div>
                            </a>
                        @else
                            <button @click="buyNow(@json($lesson->id))"
                               class="bg-[#EC6E23] text-xl !text-white border border-white rounded-full py-2 w-[170px] text-center uppercase cursor-pointer">
                                Mua ngay</button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @elseif ($lesson->video)
        <div class="video relative w-full" id="video-wrapper">
            <video id="my-video" class="video-js h-[413px] sp:h-[250px]" controls preload="auto"
                   poster="{{ $lesson->avatar_name ? url('cdn/lesson/default/' . $lesson->avatar_name) : '' }}"
                   data-setup="{}">
            </video>
        </div>
        <div class="video-quality mt-5 flex items-center font-averta-regular sp:block">
            <div class="flex items-center">
                <div id="server-vn" data-index="vn"
                     class="server-video text-base text-black py-2 px-4 sp:w-1/2 rounded-lg cursor-pointer">
                    Máy
                    chủ
                    Việt
                    Nam
                </div>
                <div id="server-jp" data-index="jp"
                     class="server-video text-base text-black ml-5 py-2 px-4 sp:w-1/2 rounded-lg cursor-pointer">
                    Máy
                    chủ Nhật Bản
                </div>
            </div>
            <div class="ml-auto flex items-center gap-x-5 sp:mt-3 sp:justify-center">
                <div id="video-quality-360p" data-index="360p"
                     class="quality-video px-2 py-3 rounded text-base cursor-pointer">
                    360p
                </div>
                <div id="video-quality-480p" data-index="480p"
                     class="quality-video px-2 py-3 rounded text-base cursor-pointer">
                    480p
                </div>
                <div id="video-quality-720p" data-index="720p"
                     class="quality-video px-2 py-3 rounded text-base cursor-pointer">
                    720p<span class="text-sm text-red-600">HD</span></div>
                <div id="video-quality-1080p" data-index="1080p"
                     class="quality-video px-2 py-3 rounded  text-base cursor-pointer">
                    1080p<span class="text-sm text-red-600">HD</span></div>
            </div>
        </div>
    @endif
    <div class="my-5 sp:px-4 ">
        <div class="mb-1 font-averta-semibold text-2xl text-[#1E1E1E] flex">
            @if($lesson->name_html)
                {!! $lesson->name_html !!}
            @else
                <span>{{ $lesson->name }}</span>
            @endif
            @if ($lesson->require)
                <span class="relative">
                    <img class="w-[12px]"
                         src="{{ asset('images/icons/' . ($isLocked ? 'locked/' : '') . 'require.png') }}"
                         alt="require.png">
                </span>
            @endif
        </div>
        <div class="opacity-70">
            Thời gian học dự kiến {{ $lesson->expect_time }} phút
        </div>
        <div class="mt-3 text-sm">
            {{ $lesson->description }}
        </div>
    </div>


    {{--  giai thich bai hoc   --}}
    @foreach($lesson->component->where('type', \App\Http\Models\LessonToTask::TYPE_EXPLAIN)->sortBy('sort') as $key => $explain)
            <div class="mb-3">
                <button id="button_explain_{{ $key }}"
                        class="border-1 rounded-full py-3 px-8 w-full shadow-md font-averta-bold text-2xl text-[#073A3B] bg-white"
                        onclick="toggleCollapse('collapse-{{ $key }}', {{ $key }})">
                    Xem giải thích chi tiết
                </button>
                <!-- Nội dung ẩn -->
                <div id="collapse-{{ $key }}" class="hidden overflow-hidden transition-[max-height] duration-500 ease-in-out max-h-0 bg-gray-100 rounded-lg">
                    <div class="p-4">
                        {!! json_decode($explain->value)[0]->content !!}
                    </div>
                </div>
            </div>

        @endforeach


    {{-- Document --}}
    @if ($isLocked && $lesson->documents->isNotEmpty())
        <div class="py-4 px-6 sp:px-4 bg-white rounded-2xl">
            <div class="text-xl text-center mb-5">
                Tài liệu
            </div>
            <div class="content relative" id="lesson-content">
                <img class="max-w-full" src="{{ asset('images/icons/bg-doc-lock.png') }}" alt="bg-lock">
                <div class="absolute top-1/2 -translate-x-1/2 left-1/2 -translate-y-1/2 text-center">
                    <div>
                        <img class="w-16" src="{{ asset('images/icons/lock-document.png') }}" alt="lock-document">
                    </div>
                    <div class="mt-5 text-[#5A5A5A] text-base font-averta-regular whitespace-nowrap">
                        Đăng ký khoá học để mở khoá <br> tài liệu của bài học này
                    </div>
                </div>
            </div>
        </div>
    @elseif($lesson->documents->isNotEmpty())
        @if ($lesson->documents->where('type', 8)->isNotEmpty())
            @php
                $document = $lesson->documents->where('type', 8)->first();
            @endphp
            @if(($courseOwner && \Carbon\Carbon::parse($courseOwner->watch_expired_day) > \Carbon\Carbon::now()) || $course->id == \App\Http\ModelsFrontend\Course::ID_COURSE_KANJI_N5)
                <div class="flex items-center justify-end mb-3">
                    <a class="btn-download" href="/cdn/pdf/{{ $document->value }}" target="_blank"
                       rel="noopener noreferrer">
                        <div class="flex items-center cursor-pointer">
                            <div class="text-[#07403F] bg-[#C1EACA] text-base font-averta-regular px-2 rounded">
                                Tải về xem ngoại tuyến
                            </div>
                            <div class="ml-3">
                                <div class="download-icon w-fit">
                                    <img class="w-[24px]" src="{{ asset('images/icons/download.png') }}" alt="download">
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            @endif
        @endif

        <div class="py-4 px-6 sp:px-4 bg-white rounded-2xl">
            <div class="text-xl text-center mb-5">
                Tài liệu
            </div>
            <div class="content" id="lesson-content">
                @foreach ($lesson->documents as $document)
                    @if ($document->type == 8)
                        <div class="app-pdf">
                            <div role="toolbar" class="toolbar">
                                <div class="pager">
                                    <p style="color: #555"><i class="fa fa-file-pdf-o" style="color: red;"></i>
                                        Tài liệu PDF</p>
                                </div>
                                <div class="page-mode">
                                    <input type="hidden" value="1" min="1"/>
                                </div>
                            </div>
                            <div class="viewport-container viewport">
                                <div role="main" id="viewport-{{ $document->id }}"></div>
                            </div>
                        </div>
                        <script>
                            $(function () {
                                initPDFViewer("/cdn/pdf/{{ $document->value }}", "#viewport-{{ $document->id }}");
                                var widthCanvas = $("#viewport-{{ $document->id }}").width();
                                $("#viewport-{{ $document->id }}").height(widthCanvas * 1.3);
                            });
                        </script>
                    @elseif ($document->type == 1)
                        <div class="mt-5">{!! $document->value !!}</div>
                    @endif
                @endforeach
            </div>
        </div>
    @endif

    {{-- Bài tập --}}
    @if ($lesson->components->whereIn('type', [3, 13])->isNotEmpty())
        <div class="flex justify-center my-8 mb-20">
            <button id="button_redirect_tab_exercise"
                    class="border-2 border-[#57D061] text-[#57D061] text-base font-beanbag rounded-full py-3 px-8">
                Làm bài tập
            </button>
        </div>
    @endif
</div>

<script>
    function toggleCollapse(id, key) {
        const element = document.getElementById(id);
        const elementParent = document.getElementById(`button_explain_${key}`);
        if (element.classList.contains('hidden')) {
            element.classList.remove('hidden');
            elementParent.classList.add("bg-[#CCF8D1]")
            elementParent.classList.remove("bg-white")
            element.style.maxHeight = element.scrollHeight + "px"; // Mở rộng theo nội dung
        } else {
            elementParent.classList.remove("bg-[#CCF8D1]")
            elementParent.classList.add("bg-white")
            element.style.maxHeight = "0px"; // Thu gọn
            setTimeout(() => element.classList.add('hidden'), 500); // Ẩn sau khi animation kết thúc
        }
    }
</script>
