// convert số thành chữ
const defaultNumbers = " hai ba bốn năm sáu bảy tám chín";
const chuHangDonVi = ("1 một" + defaultNumbers).split(" ");
const chuHangChuc = ("lẻ mười" + defaultNumbers).split(" ");
const chuHangTram = ("không một" + defaultNumbers).split(" ");
const dvBlock = "1 nghìn triệu tỷ".split(" ");

function convert_block_three(number) {
  if (number == "000") return "";
  var _a = number + ""; //Convert biến 'number' thành kiểu string
  switch (_a.length) {
    case 0:
      return "";
    case 1:
      return chuHangDonVi[_a];
    case 2:
      return convert_block_two(_a);
    case 3:
      var chuc_dv = "";
      if (_a.slice(1, 3) != "00") {
        chuc_dv = convert_block_two(_a.slice(1, 3));
      }
      var tram = chuHangTram[_a[0]] + " trăm";
      return tram + " " + chuc_dv;

  }
}

function convert_block_two(number) {
  var dv = chuHangDonVi[number[1]];
  var chuc = chuHangChuc[number[0]];
  var append = "";
  if (number[0] > 0 && number[1] == 5) {
    dv = "lăm";
  }
  if (number[0] > 1) {
    append = " mươi";
    if (number[1] == 1) {
      dv = " mốt";
    }
  }
  return chuc + "" + append + " " + dv;
}

//convert number to text
function to_vietnamese(number) {
  var str = parseInt(number) + "";
  var i = 0;
  var arr = [];
  var index = str.length;
  var result = [];
  var rsString = "";
  if (index == 0 || str == "NaN") {
    return "";
  }
  while (index >= 0) {
    arr.push(str.substring(index, Math.max(index - 3, 0)));
    index -= 3;
  }
  for (i = arr.length - 1; i >= 0; i--) {
    if (arr[i] != "" && arr[i] != "000") {
      result.push(convert_block_three(arr[i]));

      if (dvBlock[i]) {
        result.push(dvBlock[i]);
      }
    }
  }
  rsString = result.join(" ");
  return rsString.replace(/[0-9]/g, "").replace(/ /g, " ").replace(/ $/, "");
}

$.ajaxSetup({
  headers: {
    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
  },
});
Vue.component("v-select", VueSelect.VueSelect);

const router = new VueRouter({
  mode: "history",
});

Vue.filter("dateTimeToMinute", function (value) {
  return value ? moment(value).format("HH:mm:ss DD/MM/YYYY") : "--";
});

Vue.filter("readableDate", function (value) {
  return value ? moment(value).format("DD/MM/YYYY") : "--";
});

Vue.filter("decimal", (number) => {
  return new Intl.NumberFormat("vi-VN").format(number);
});
Vue.use(DatePicker);
const invoiceScreen = new Vue({
  router: router,
  el: "#vip-invoice--screen",
  components: {
    paginate: VuejsPaginate,
  },
  data() {
    return {
      url: window.location.origin,
      adminId: adminId,
      loading: false,
      addUserLoading: false,
      activeLoading: false,
      activeInvoiceLoading: false,
      createInvoiceLoading: false,
      showAddInvoiceForm: false,
      showNoteModal: false,
      showLogModal: false,
      showAddGroupModal: false,
      showInvoiceDept: false,
      depositInvoice: {},
      currentNote: "",
      currentLog: null,
      currentAddCourseData: {},
      joinedGroupListByUser: [],
      groupListByUser: [],
      groupListByCombo: [],
      offlineGroups: [],
      joinedOfflineGroups: [],
      search: "",
      filterCount: 0,
      filter: {
        id: "",
        uuid: "",
        email: "",
        phone: "",
        name: "",
        comboId: [],
        courseId: undefined,
        groupId: undefined,
        paymentMethod: undefined,
        time_by: undefined,
        time_from: undefined,
        time_to: undefined,
        sort: "",
        sale: undefined,
        status: "new",
        isVip: undefined,
        voucher: undefined,
        admin_create: undefined,
        state: undefined,
        isAttached: undefined,
        isActivated: undefined,
        currency: undefined,
        refund: "",
        location: "",
        payer: "",
        modifyStatus: "",
        exportStatus: "",
        depositStatus: "",
        page: 1,
        per_page: 20,
        total_page: 10,
      },
      results: [],
      total_result: 0,
      currentFormUser: null,
      formError: {},
      formData: {
        email: "",
        password: "",
        fullname: "",
        phone: "",
        facebook: "",
        chat: "",
        address: "",
        department: "1",
        comboId: null,
        comboType: [],
        price: undefined,
        currency: "vnd",
        coupon: "",
        paymentMethod: "",
        paidAt: moment().format("YYYY-MM-DD HH:mm:ss"),
        note: undefined,
        groupId: [],
        isVip: false,
        isOnline: true,
        activeOnline: false,
        discountMoney: 0,
        paidMoney: "",
        bookIds: [],
        refundBooks: [],
        rewardId: undefined,
        oldPrice: undefined,
        paidFor: undefined,
        bonusComboId: undefined,
      },
      bookData: {
        postCode: "",
        address: "",
        price: 0,
        payer: "dmr",
        totalPrice: 0,
        location: "",
        status: "packing",
        refundFault: "",
        refundShip: 0,
        modifyStatus: "",
      },
      bookList: [],
      reservations: [],
      showInvoiceDetail: false,
      printInvoice: {},
      cloneInvoice: false,
      offlineData: {
        gift: {
          bag: false,
          book: false,
        },
      },
      userAvailableRewards: [],
      bonus: {
        courseId: "",
        days: 1,
      },
      bonuses: [
        {
          courseId: "",
          days: 1,
        }
      ],
      courses: [],
      editingInvoice: null,
      validCourses: [
        {id: 17, name: 'N1'},
        {id: 16, name: 'N2'},
        {id: 3, name: 'N3'},
        {id: 40, name: 'N4'},
        {id: 39, name: 'N5'},
      ],
      byPassGroupLevel: 0,
    };
  },
  computed: {
    relevantInvoices() {
      if (this.depositInvoice.paid_for_id && this.depositInvoice.deposit_invoice) {
        return [this.depositInvoice.deposit_invoice, ...this.depositInvoice.deposit_invoice.relevant_invoices]
      } else {
        return [this.depositInvoice, ...this.depositInvoice.relevant_invoices]
      }
    },
    isOfflineFilter() {
      return this.$route.query.isVip && Number(this.$route.query.isVip) === 3;
    },
    isOfflineInvoice() {
      return this.currentAddCourseData.invoice.product_type === "offline";
    },
    joinedList() {
      if (this.isOfflineInvoice) return this.joinedGroupListByUser;
      return this.joinedGroupListByUser.map((group) => {
        const idx = this.reservations.findIndex((r) => r.id === group.id);
        group.userCount =
          idx === -1 ? group.group_user.length : this.reservations[idx].count;
        return group;
      });
    },
    groupList() {
      if (this.isOfflineInvoice) return this.groupListByUser;
      return this.groupListByUser.map((group) => {
        const idx = this.reservations.findIndex((r) => r.id === group.id);
        group.userCount =
          idx === -1 ? group.group_user.length : this.reservations[idx].count;
        return group;
      });
    },
    groupListCombo() {
      return this.groupListByCombo.map((group) => {
        const idx = this.reservations.findIndex((r) => r.id === group.id);
        group.userCount =
          idx === -1 ? group.group_user.length : this.reservations[idx].count;

        return group;
      });
    },
    subjects() {
      return {
        invoice_creation: "Tạo đơn",
        user_id_reservation: "Sửa email sau khi giữ chỗ",
        user_attachment: "Thêm vào lớp",
        user_detachment: "Xoá khỏi lớp",
        information_changed: "Thay đổi thông tin",
        online_course_activation: "Kích hoạt khoá online",
        active_bonus: "Kích hoạt khoá tặng kèm",
      };
    },
    columns() {
      return {
        invoice: {
          id: "ID",
          info_contact: {
            name: "Tên học viên",
            phone: "Số điện thoại",
            email: "Email",
            facebook: "Link facebook",
            chat: "Link chăm sóc",
            address: "Địa chỉ",
          },
          price: "Giá",
          currency: "Đơn vị",
          discount_money: "Chiết khấu",
          payment_method_id: "Phương thức thanh toán",
          department_id: "Cơ sở",
          paid_money: "Thực thu",
          paid_at: "Ngày thanh toán",
          note: "Note",
          books: "Sách",
          combo: "Sản phẩm",
          bonus: "Quà tặng",
          book_info: {
            postCode: "(Sách) Mã bưu điện",
            address: "(Sách) Địa chỉ",
            price: "(Sách) giá",
            payer: "(Sách) Người trả phí vc",
            totalPrice: "(Sách) totalPrice",
            phone: "(Sách) SĐT",
            status: "(Sách) Trạng thái",
            location: "(Sách) Vị trí",
            refundFault: "(Sách) Lỗi do",
            refundShip: "(Sách) Phí ship hoàn",
            modifyStatus: "(Sách) Trạng thái sách",
            exportStatus: "(Sách) Trạng thái in",
            exportDate: "(Sách) Ngày in",
          },
          refund_books: "Hoàn sách"
        },
        community_group_user: {
          group_id: "ID nhóm",
          user_id: "ID học viên",
        },
        course_owner: {
          watch_expired_day: "Ngày hết hạn",
        },
        book_info: {
          modifyStatus: "Đơn sách sửa",
        },
        invoice_bonus: {
          course_id: "ID khoá học",
          days: "Số ngày",
        },
      };
    },
    comboList() {
      return comboList;
    },
    vipComboList() {
      return vipComboList;
    },
    courseList() {
      return courseList;
    },
    paymentMethodList() {
      return paymentMethodList;
    },
    saleList() {
      return saleList;
    },
    departmentList() {
      return departmentList;
    },
    currentGroup() {
      const groups = this.formData.isOnline
        ? this.groupListByCombo
        : this.offlineGroups;
      return groups.filter((group) => this.formData.groupId.includes(group.id));
    },
    currentFormOwner() {
      return this.currentFormUser ? this.currentFormUser.course_owner : [];
    },
    currentFormEntryPoint() {
      const checkpointLogs = this.currentFormUser?.checkpoint_logs || [];
      const level = this.currentGroup[0]?.vip_level;
      let currentLog = null;
      if (level) {
        currentLog = checkpointLogs.find((log) => {
          return log.lesson.get_course.name === level;
        });
        return currentLog;
      }
      return null;
    },
    comboes() {
      const comboes = this.formData.isVip
        ? this.vipComboList.filter((c) =>
            this.formData.isOnline ? c.type !== 5 : c.type === 5
          )
        : this.comboList;
      const result = comboes.filter((c) => {
        if (
          this.formData.comboId &&
          c.id == this.formData.comboId.id &&
          c.type == this.formData.comboId.type
        )
          return false;
        return true;
      });
      return result;
    },
    currentCombo() {
      return this.formData.comboId;
    },
    currentOwnerWatchExpired() {
      const aliases = {
        N1: "N1",
        N2: "N2",
        N3: "N3",
        N4: "Sơ cấp N4",
        N5: "Sơ cấp N5",
        kaiwacb: "Kaiwa Sơ Cấp",
        kaiwasc: "Kaiwa Sơ Cấp",
        kaiwatc: "Kaiwa Trung Cấp",
        kaiwatc2: "Kaiwa Trung Cấp",
        kaiwanc: "Kaiwa Nâng Cao",
        ldn1: "Luyện đề N1",
        ldn2: "Luyện đề N2",
        ldn3: "Luyện đề N3",
      };
      if (this.currentFormOwner.length && this.currentGroup) {
        return this.currentFormOwner.filter((owner) =>
          this.currentGroup
            .map(
              (group) =>
                aliases[
                  this.formData.isOnline
                    ? group.vip_level
                    : `N${6 - group.level_of_n}`
                ]
            )
            .includes(owner.title)
        );
      }
      return [];
    },
    getGroupWatchExpiredStatus() {
      if (this.currentFormOwner.length && this.currentAddCourseData) {
        const tmp = { ...this.currentAddCourseData };
        const aliases = {
          N1: "N1",
          N2: "N2",
          N3: "N3",
          N4: "Sơ cấp N4",
          N5: "Sơ cấp N5",
          kaiwacb: "Kaiwa Sơ Cấp",
          kaiwasc: "Kaiwa Sơ Cấp",
          kaiwatc: "Kaiwa Trung Cấp",
          kaiwatc2: "Kaiwa Trung Cấp",
          kaiwanc: "Kaiwa Nâng Cao",
          ldn1: "Luyện đề N1",
          ldn2: "Luyện đề N2",
          ldn3: "Luyện đề N3",
        };
        console.log("tmp course name ",tmp.courseName)
        tmp.courseName = aliases[tmp.courseName];
        const owner = _.find(this.currentFormOwner, ["title", tmp.courseName]);

        return owner ? owner.watch_expired_day : null;
      }
      return null;
    },
    // Kiểm tra đơn hàng có sách
    hasBook() {
      if (this.formData.bookIds.length && this.formData.isOnline) return true;
      return false;
    },
    adminRole() {
      return adminRole;
    },
  },
  watch: {
    filter: {
      handler: function (val, oldVal) {
        const vm = this;
      },
      deep: true,
    },
    "formData.comboId"(value) {
      if (!this.formData.id && this.formData.comboId) {
        const combo = this.formData.comboId;
        if (combo && !this.cloneInvoice) this.priceCalculate();
        this.getGroupByCombo(value, this.search);
      }
    },
    "formData.department"(value) {
      if (!this.formData.id && this.formData.comboId) {
        this.getGroupByCombo(this.formData.comboId, this.search);
      }
    },
    "filter.time_by"(value) {
      if (value === undefined) {
        this.filter.time_from = undefined;
        this.filter.time_to = undefined;
      }
    },
    "bookData.location"(value) {
      this.getBookList();
    },
    "formData.email"(value) {
      this.emailChangeHandler(value);
    },
  },

  methods: {
    comboSelected(combo) {
      if (combo.services && JSON.parse(combo.services) && JSON.parse(combo.services).courses) {
        const services = JSON.parse(combo.services);
        this.bonuses = services.courses.map((course) => {
          return {
            courseId: Number(course),
            days: Number(services.course_watch_expired_value),
          };
        });
      }
    },
    useReward(reward) {
      if (this.formData.rewardId == reward.pivot.id) {
        this.formData.price = this.formData.oldPrice;
        this.formData.rewardId = undefined;
        return;
      }
      if (!this.formData.price) {
        alert("Không thể áp dụng voucher. Chưa điền giá");
        return;
      }
      if (!this.formData.rewardId) {
        this.formData.oldPrice = this.formData.price;
      } else {
        this.formData.price = this.formData.oldPrice;
      }
      this.formData.rewardId = reward.pivot.id;
      if (reward.type === "discount_money") {
        this.formData.price = this.formData.price - reward.value;
      }
      if (reward.type === "discount_percent") {
        this.formData.price = this.formData.price * (1 - reward.value / 100);
      }
    },
    emailChangeHandler: _.debounce(function (email) {
      if (!email) return;
      this.getUserAvailableRewards(email);
    }, 1000),
    getUserAvailableRewards(email) {
      const vm = this;
      const data = {
        email: email,
      };
      $.get(
        window.location.origin + "/backend/vip/get-user-available-rewards",
        data,
        function (res) {
          if (res.code === 200) {
            vm.userAvailableRewards = res.data;
          } else {
            alert("Có lỗi! Liên hệ dev!!!");
          }
          vm.loading = false;
        }
      );
    },
    checkDuplicate(groupUsers) {
      const dup = [];
      groupUsers.forEach((group) => {
        const tmp = groupUsers.filter(
          (item) =>
            item.invoice_id !== null && item.invoice_id === group.invoice_id
        );
        if (tmp.length > 1) {
          dup.push(group.user_id);
        }
      });
      return dup;
    },
    isReservationCompleted(logs) {
      if (!logs || logs.length === 0) {
        return false;
      }
      return logs.some((log) => log.target === "user_id_reservation");
    },
    toggleStatus(status) {
      this.filter.status = status;
      this.applyFilter();
    },
    getGroupStatus(status) {
      switch (status) {
        case 1:
          return "Đang tuyển";
        case 2:
          return "Đang học";
        case 3:
          return "Đã kết thúc";
        default:
          return "Đang tuyển";
      }
    },
    getGroupStatusClass(status) {
      switch (status) {
        case 1:
          return "badge-info";
        case 2:
          return "badge-success";
        case 3:
          return "badge-danger";
        default:
          return "Đang tuyển";
      }
    },
    closeAddGroupModal() {
      this.groupListByUser = [];
      this.groupListByCombo = [];
      this.showAddGroupModal = false;
    },
    openCreate(isVip, isOnline = true) {
      const vm = this;
      this.closeInvoiceDept();
      this.showAddInvoiceForm = true;
      this.formData.isVip = isVip;
      this.formData.isOnline = isOnline;
      setTimeout(function () {
        $("#paidAt")
          .datetimepicker({
            language: "vi",
          })
          .on("dp.change", function (event) {
            vm.onChangePaidAt(event);
          });
      }, 100);
    },
    limiter(e) {
      if (e.length > 2) {
        console.log(" you can only select two", e);
        e.pop();
      }
    },
    editInvoice(invoice) {
      this.editingInvoice = _.cloneDeep(invoice);
      let comboId = [];
      if (["vip_combo", "offline"].includes(invoice.product_type)) {
        if (invoice.vip_combo) {
          comboId = invoice.vip_combo;
        }
      } else {
        if (invoice.combo) {
          if (!invoice.book || invoice.combo.id !== invoice.book.id) {
            comboId = invoice.combo;
          }
        }
      }
      if (invoice.gift) {
        let data = JSON.parse(invoice.gift);
        this.offlineData.gift.bag = data.bag;
        this.offlineData.gift.book = data.book;
      } else {
        this.offlineData.gift.bag = 0;
        this.offlineData.gift.book = 0;
      }
      this.formData = {
        id: invoice.id,
        email: invoice.info_contact.email,
        password: "",
        fullname: invoice.info_contact.name,
        phone: invoice.info_contact.phone,
        facebook: invoice.info_contact.facebook,
        chat: invoice.info_contact.chat,
        address: invoice.info_contact.address || "",
        comboId: comboId,
        coupon: invoice.coupon_id,
        price: invoice.price,
        currency: invoice.currency,
        paymentMethod: invoice.payment_method_id,
        department: invoice.department?.id || null,
        paidAt: invoice.paid_at,
        note: invoice.note,
        groupId: "",
        paymentStatus: invoice.payment_status,
        invoiceStatus: invoice.invoice_status,
        logs: invoice.logs,
        product_type: invoice.product_type,
        active_time: invoice.active_time,
        discountMoney: invoice.discount_money,
        paidMoney: invoice.paid_money,
        bookIds: invoice.books,
        refundBooks: invoice.refund_books,
        applied_reward: invoice.applied_reward,
      };
      this.bonus = {
        days: invoice.bonus?.days || 1,
        courseId: invoice.bonus?.course_id || "",
      };
      if (invoice.bonuses?.length) {
        this.bonuses = invoice.bonuses?.map((bonus) => {
          return {
            courseId: bonus.course_id,
            days: bonus.days,
          };
        })
      } else {
        this.bonuses = [this.bonus]
      }


      // Dữ liệu đơn hàng sách
      if (!invoice.book_info) {
        this.bookData = {
          postCode: "",
          address: "",
          price: 0,
          payer: "dmr",
          totalPrice: 0,
          location: "",
          status: "packing",
          refundFault: "",
          refundShip: 0,
          modifyStatus: "",
        };
      } else this.bookData = { ...invoice.book_info };

      this.formData.isVip =
        ["vip_combo", "vip_course"].includes(invoice.product_type) ||
        invoice.product_type === "offline";
      this.formData.isOnline = invoice.product_type !== "offline";
      this.closeInvoiceDept();
      this.showAddInvoiceForm = true;
      this.getUserAvailableRewards(this.formData.email);
    },
    closeAddInvoiceForm() {
      this.groupListByCombo = [];
      this.showAddInvoiceForm = false;
      this.resetAddInvoiceForm();
    },
    resetAddInvoiceForm() {
      this.formData = {
        email: "",
        password: "",
        fullname: "",
        phone: "",
        facebook: "",
        chat: "",
        address: "",
        comboId: null,
        price: undefined,
        currency: "vnd",
        coupon: "",
        paymentMethod: undefined,
        department: undefined,
        paidAt: moment().format("YYYY-MM-DD HH:mm:ss"),
        note: undefined,
        groupId: [],
        isVip: false,
        activeOnline: false,
        discountMoney: 0,
        paidMoney: 0,
        bookIds: [],
        refundBooks: [],
        rewardId: undefined,
        oldPrice: undefined,
      };
      (this.bookData = {
        postCode: "",
        address: "",
        price: 0,
        payer: "dmr",
        totalPrice: 0,
        location: "",
        status: "packing",
        refundFault: "",
        refundShip: 0,
        modifyStatus: "",
      }),
        (this.cloneInvoice = false);
      this.currentAddCourseData = {};
      this.formError = {};
      this.offlineData.gift = {
        bag: false,
        book: false,
      };
      this.bonus = {
        courseId: "",
        days: 1,
      };
      this.bonuses = [
        {
          courseId: "",
          days: 1,
        }
      ];
    },
    // gọi hàm call api lấy invoices theo filter và đẩy filter lên param của url khi thay đổi pagination
    changePage: function (pageNum) {
      const vm = this;
      vm.filter.page = pageNum;
      var filter = _.omit(
        _.pickBy(vm.filter, function (value, key) {
          return value !== undefined && value !== null && value !== "";
        }),
        ["total_page"]
      );
      vm.getList(filter);

      this.$router.replace(window.location.pathname + "?" + $.param(filter));
    },
    // như trên nhưng là khi áp dụng bộ lọc, reset page và per_page về default
    applyFilter: function () {
      const vm = this;

      vm.filter.page = 1;
      var filter = _.omit(
        _.pickBy(vm.filter, function (value, key) {
          return value !== undefined && value !== null && value !== "";
        }),
        ["total_page", "page"]
      );
      vm.getList(filter);

      // console.log(filter)
      this.$router.replace(window.location.pathname + "?" + $.param(filter));
    },
    onChangeCheckbox: function (param, event) {
      const vm = this;

      vm.filter[param] = event.target.checked ? 1 : 0;
      vm.applyFilter();
    },
    // call api lấy danh sách invoices
    // truyền vào filter
    // set các biến trạng thái tmp để có thể dùng v-if, v-model
    getList: function (filter) {
      const vm = this;

      vm.loading = true;
      setTimeout(function () {
        $.get(
          window.location.origin + "/backend/vip/invoices",
          filter,
          function (res) {
            if (res.code == 200) {
              vm.results = res.data.data.map(function (result) {
                return result;
              });
              vm.filter.total_page = res.data.last_page;
              vm.total_result = res.data.total;
            } else {
              alert("Có lỗi! Liên hệ dev!!!");
            }
            vm.loading = false;
          }
        );
      }, 200);
      this.filterCount++;
    },
    // dùng plugin jquery deparam lấy param từ url và convert thành object, thay đổi filter theo object vừa lấy được
    setFilterByUrl: function () {
      const vm = this;
      var filterByUrl = $.deparam.querystring();
      _.forEach(filterByUrl, function (value, key) {
        vm.filter[key] = value == parseInt(value) ? parseInt(value) : value;
      });
    },
    // reset bộ lọc, gọi lại hàm applyFilter để lấy danh sách invoices và đẩy param lên url
    resetFilter: function () {
      const vm = this;
      vm.filter = {
        id: "",
        uuid: "",
        email: "",
        phone: "",
        name: "",
        comboId: [],
        courseId: undefined,
        groupId: undefined,
        paymentMethod: undefined,
        time_by: undefined,
        time_from: undefined,
        time_to: undefined,
        sort: "",
        sale: undefined,
        status: "new",
        isVip: undefined,
        voucher: undefined,
        admin_create: undefined,
        state: undefined,
        isAttached: undefined,
        isActivated: undefined,
        currency: undefined,
        refund: "",
        location: "",
        payer: "",
        modifyStatus: "",
        exportStatus: "",
        depositStatus: "",
        page: 1,
        per_page: 20,
        total_page: 10,
      };

      this.applyFilter();
    },
    // thay đổi filter theo datetime
    onChangeDatetime: function (event) {
      const vm = this;
      vm.filter[event.target.name] = moment(event.date).format(
        "YYYY-MM-DD HH:mm"
      );
    },
    onChangePaidAt(event) {
      const vm = this;
      vm.formData.paidAt = moment(event.date).format("YYYY-MM-DD HH:mm");
    },
    getUser(userId) {
      const vm = this;
      const data = {
        email: vm.formData.email,
        userId: userId,
      };
      $.get(
        window.location.origin + "/backend/vip/get-user",
        data,
        function (res) {
          if (res.code == 200) {
            vm.currentFormUser = res.data;
          }
        }
      );
    },
    createInvoice() {
      const vm = this;
      if (this.currentAddCourseData.isCreateUser && !this.formData.password) {
        this.$set(this.formError, "password", ["Vui lòng điền mật khẩu"]);
        return;
      }

      // if (!this.formData.phone) {
      //   this.$set(this.formError, "phone", ["Vui lòng điền số điện thoại"]);
      //   return;
      // }

      // if (!this.hasBook && !this.formData.address) {
      //   this.$set(this.formError, "address", ["Vui lòng điền địa chỉ"]);
      //   return;
      // }

      // if (this.hasBook && !this.bookData.address) {
      //   this.$set(this.formError, "bookData.address", ["Vui lòng điền địa chỉ"]);
      //   return;
      // }

      
      const data = {
        ...this.formData,
        isVip: this.formData.isVip ? 1 : 0,
        isOnline: this.formData.isOnline ? 1 : 0,
        comboId: this.formData.comboId,
        comboType: vm.formData.comboId?.type,
        activeOnline: this.formData.activeOnline ? 1 : 0,
        gift: JSON.stringify(this.offlineData.gift),
        bookData: { ...this.bookData },
        bonus: { ...this.bonus },
        bonuses: this.bonuses
      };
      vm.handleCreateInvoice(data);
    },
    handleCreateInvoice(data) {
      const vm = this;
      vm.createInvoiceLoading = true;

      $.post(
        window.location.origin + "/backend/vip/create-invoice",
        data,
        function (res) {
          if (res.code == 200) {
            if (vm.filter.status === "new") {
              if (res.data.payment_status === "unpaid") {
                vm.results.unshift(res.data);
              }
            } else if (vm.filter.status === "completed") {
              if (
                res.data.payment_status === "paid" ||
                res.data.invoice_status === "completed"
              ) {
                vm.results.unshift(res.data);
              }
            } else if (vm.filter.status === res.data.invoice_status) {
              vm.results.unshift(res.data);
            }
            vm.closeAddInvoiceForm();
          } else if (res.code == 409) {
            const conf = confirm(res.msg);
            if (conf) {
              const data = {
                ...vm.formData,
                isVip: vm.formData.isVip ? 1 : 0,
                isOnline: vm.formData.isOnline ? 1 : 0,
                comboId: vm.formData.comboId,
                comboType: vm.formData.comboId?.type,
                activeOnline: vm.formData.activeOnline ? 1 : 0,
                forceDuplicate: 1,
                bookData: { ...vm.bookData },
                gift: JSON.stringify(vm.offlineData.gift),
                bonus: { ...vm.bonus },
              };
              console.log("data", data);
              vm.handleCreateInvoice(data);
            }
          } else {
            if (res.status == "error") {
              vm.formError = res.detail;
            } else {
              alert("Có lỗi! Liên hệ dev");
            }
          }
          vm.loading = false;
        }
      )
        .fail(function (res) {
          if (res.status === 400) {
            alert(res.responseJSON.data.msg);
          } else {
            alert("Có lỗi! Liên hệ dev");
          }
        })
        .always(function () {
          vm.createInvoiceLoading = false;
        });
    },
    hasDuplicatesByKey(collection, key) {
      const seen = new Set();
      for (const item of collection) {
        if (seen.has(item[key])) {
          return true;
        }
        seen.add(item[key]);
      }
      return false;
    },
    updateInvoice() {
      const vm = this;
      console.log('this.formData', this.formData);
      console.log('this.bonus', this.bonus);
      console.log('this.editingInvoice', this.editingInvoice);
      console.log('this.courses', this.courses);

      if (this.currentAddCourseData.isCreateUser && !this.formData.password) {
        this.$set(this.formError, "password", ["Vui lòng điền mật khẩu"]);
        return;
      }

      if (this.hasDuplicatesByKey(this.bonuses, "courseId")) {
        this.$set(this.formError, "bonuses", ["Mắc gì chọn 2 khóa trùng nhau ¯\\_(ツ)_/¯"]);
        return;
      }

      // Log edit invoice history
      const editLogs = [];
      if (
        this.formData.bookIds?.length !== this.editingInvoice.books?.length ||
        this.formData.bookIds?.reduce((s, a) => s + a.id, 0) !== this.editingInvoice.books?.reduce((s, a) => s + a.id, 0) ||
        this.formData.bookIds?.reduce((s, a) => s + parseInt(a.quantity), 0) !== this.editingInvoice.books?.reduce((s, a) => s + parseInt(a.quantity), 0)
      ) {
        let from = '';
        for (let i = 0; i < this.editingInvoice.books.length; i++) {
          const book = this.editingInvoice.books[i];
          from += `${i === 0 ? '' : ', '}${book.name} (${book.quantity})`;
        }

        let to = '';
        for (let i = 0; i < this.formData.bookIds.length; i++) {
          const book = this.formData.bookIds[i];
          to += `${i === 0 ? '' : ', '}${book.name} (${book.quantity})`;
        }

        editLogs.push({
          from,
          to,
          column: "invoice.books",
          target: this.editingInvoice.id,
          subject: "information_changed",
        });
      }

      if (
        this.formData.refundBooks?.length !== this.editingInvoice.refund_books?.length ||
        this.formData.refundBooks?.reduce((s, a) => s + a.id, 0) !== this.editingInvoice.refund_books?.reduce((s, a) => s + a.id, 0) ||
        this.formData.refundBooks?.reduce((s, a) => s + parseInt(a.quantity), 0) !== this.editingInvoice.refund_books?.reduce((s, a) => s + parseInt(a.quantity), 0)
      ) {
        let from = '';
        if (this.editingInvoice.refund_books) {
          for (let i = 0; i < this.editingInvoice.refund_books.length; i++) {
            const book = this.editingInvoice.refund_books[i];
            from += `${i === 0 ? '' : ', '}${book.name} (${book.quantity})`;
          }
        }

        let to = '';
        if (this.formData.refundBooks) {
          for (let i = 0; i < this.formData.refundBooks.length; i++) {
            const book = this.formData.refundBooks[i];
            to += `${i === 0 ? '' : ', '}${book.name} (${book.quantity})`;
          }
        }

        editLogs.push({
          from,
          to,
          column: "invoice.refund_books",
          target: this.editingInvoice.id,
          subject: "information_changed",
        });
      }

      if (this.formData.comboId &&
        (
          (!['vip_combo', 'offline'].includes(this.editingInvoice.product_type) && this.formData.comboId?.id !== this.editingInvoice.combo?.id) ||
          (['vip_combo', 'offline'].includes(this.editingInvoice.product_type) && this.formData.comboId?.id !== this.editingInvoice.vip_combo?.id)
        )
      ) {
        const newLogCombo = {
          from: (['vip_combo', 'offline'].includes(this.editingInvoice.product_type) ? this.editingInvoice.vip_combo?.name : this.editingInvoice.combo?.name) || '',
          to: this.formData.comboId?.name || '',
          column: "invoice.combo",
          target: this.editingInvoice.id,
          subject: "information_changed",
        };
        if (newLogCombo.from !== newLogCombo.to) {
          editLogs.push(newLogCombo);
        }
      }

      if ((JSON.stringify(this.bonuses) !== JSON.stringify(this.editingInvoice.bonuses)) && this.bonuses.length > 0 && this.bonuses[0].courseId) {
        this.bonuses.forEach((bonus, index) => {
            if (
                !this.editingInvoice.bonuses[index] ||
                (this.editingInvoice.bonuses[index].course_id !== bonus.courseId ||
                this.editingInvoice.bonuses[index].days !== bonus.days)
            ) {
                editLogs.push({
                from: this.editingInvoice.bonuses[index] ? `${this.courses.find(i => i.id === this.editingInvoice.bonuses[index].course_id).name} (${this.editingInvoice.bonuses[index].days})` : '',
                to: `${this.courses.find(i => i.id === bonus.courseId).name} (${bonus.days})`,
                column: "invoice.bonus",
                target: this.editingInvoice.id,
                subject: "information_changed",
                });
            }
        })
      }

      if (editLogs.length > 0) {
        this.formData.editLogs = editLogs;
      }

      // return;

      const data = {
        ...this.formData,
        isVip: this.formData.isVip ? 1 : 0,
        isOnline: this.formData.isOnline ? 1 : 0,
        gift: JSON.stringify(this.offlineData.gift),
        bookData: this.bookData,
        bonus: { ...vm.bonus },
        bonuses: [...vm.bonuses]
      };
      $.post(
        window.location.origin + "/backend/vip/update-invoice",
        data,
        function (res) {
          if (res.code == 200) {
            vm.getInvoice(vm.formData.id);
            vm.closeAddInvoiceForm();
          } else {
            if (res.status == "error") {
              vm.formError = res.detail;
            } else {
              alert("Có lỗi! Liên hệ dev");
            }
          }
          vm.loading = false;
        }
      );
    },
    activeBonusCourse(invoice) {
      const vm = this;
      if (!invoice.bonus) return;
      vm.createInvoiceLoading = true;
      var r = confirm("Bạn có chắc chắn muốn kích hoạt đơn hàng này ???");
      if (r) {
        $.post(
          window.location.origin + "/backend/vip/active-bonus/" + invoice.id,
          function (res) {
            vm.results = vm.results.map((item) => {
              if (item.id === invoice.id) {
                item.bonus.status = 0;
              }
              return item;
            });
            vm.createInvoiceLoading = false;
          }
        );
      }
    },
    getInvoice(id) {
      const vm = this;
      $.get(
        window.location.origin + "/backend/vip/invoices/" + id,
        function (res) {
          if (res.code == 200) {
            vm.results = vm.results.map((o) => {
              if (o.id === res.data.id) o = res.data;
              return o;
            });
          }
        }
      );
    },
    exportExcel() {
      const vm = this;

      var filter = _.omit(
        _.pickBy(vm.filter, function (value, key) {
          return value !== undefined && value !== null && value !== "";
        }),
        ["total_page"]
      );
      var url =
        window.location.origin +
        "/backend/vip/invoices/export" +
        window.location.search;
      window.open(url);
    },
    exportBookExcel() {
      var vm = this;
      var filter = _.omit(
        _.pickBy(vm.filter, function (value, key) {
          return value !== undefined && value !== null && value !== "";
        }),
        ["total_page"]
      );
      var url =
        window.location.origin +
        "/backend/vip/book-invoice/export" +
        window.location.search;
      window.open(url);

      vm.results = vm.results.map((r) => {
        if (r.book_info) {
          r.book_info.exportStatus = 'print';
        }
        return r;
      });

      this.bookData.exportStatus = 'print';
    },
    searchUser(event) {
      const vm = this;
      const email = event.target.value;
      vm.loading = true;
      $.get(
        window.location.origin + "/backend/vip/invoices",
        filter,
        function (res) {
          if (res.code == 200) {
            vm.results = res.data.results.map(function (result) {
              return result;
            });
            vm.filter.total_page = res.data.total_page;
            vm.total_result = res.data.total_result;
          } else {
            alert("Có lỗi! Liên hệ dev!!!");
          }
          vm.loading = false;
        }
      );
    },
    showNote(note) {
      this.currentNote = note;
      this.showNoteModal = true;
      this.closeInvoiceDept();
    },
    showLog(log) {
      this.currentLog = log;
      this.showLogModal = true;
      this.closeInvoiceDept();
    },
    createUser() {
      this.$set(this.currentAddCourseData, "isCreateUser", true);
    },
    showAddGroup(invoice, userId, courseName) {
      this.currentAddCourseData = {
        isCreateUser: false,
        invoice: invoice,
        userId: userId,
        courseName: courseName,
        search: "",
      };
      this.getJoinedGroupByCourse(userId, courseName, invoice.product_type);
      this.getUserGroupByCourse(userId, courseName, invoice.id, "", invoice.product_type);
      this.getUser(userId);
      this.closeInvoiceDept()
      this.showAddGroupModal = true;
    },
    getGroupUser(userId, groups) {
      return _.find(groups, (o) => o.user_id === userId) ?? {};
    },
    setPoint(group) {
      const point = window.prompt("Nhập điểm đầu vào");
      if (point !== null) {
        const data = {
          userId: group.user_id,
          groupId: group.group_id,
          point: point,
        };
        $.post(
          window.location.origin + "/backend/vip/set-entry-point",
          data,
          function (res) {
            if (res.code == 200) {
              group.entry_point = point;
            } else {
              alert("Có lỗi! Liên hệ bạn Quang 18 tủi");
            }
            vm.loading = false;
          }
        );
      }
    },
    searchGroup() {
      this.getUserGroupByCourse(
        this.currentAddCourseData.userId,
        this.currentAddCourseData.courseName,
        this.currentAddCourseData.invoice.id,
        this.currentAddCourseData.search,
        this.currentAddCourseData.invoice.product_type,
        this.currentAddCourseData.byPassGroupLevel ? 1 : 0
      );
    },
    searchGroupByCourse() {
      this.getGroupByCombo(this.formData.comboId, this.search);
    },
    getJoinedGroupByCourse(userId, courseName, type) {
      const vm = this;
      const data = {
        userId: userId,
        courseName: courseName,
        isOffline:
          this.currentAddCourseData.invoice.product_type === "offline" ? 1 : 0,
        type: type
      };
      $.get(
        window.location.origin + "/backend/vip/get-joined-group-by-course",
        data,
        function (res) {
          if (res.code == 200) {
            vm.joinedGroupListByUser = res.data;
          } else {
            alert("Có lỗi! Liên hệ dev!!!");
          }
          vm.loading = false;
        }
      );
    },
    getUserGroupByCourse(userId, courseName, invoiceId, search = "", type, byPassGroupLevel = 0) {
      const vm = this;

      const data = {
        userId: userId,
        courseName: courseName,
        invoiceId: invoiceId,
        search: search,
        type: type,
        byPassGroupLevel: byPassGroupLevel,
      };
      $.get(
        window.location.origin + "/backend/vip/get-user-group-by-course",
        data,
        function (res) {
          if (res.code == 200) {
            vm.groupListByUser = res.data;
          } else {
            alert("Có lỗi! Liên hệ dev!!!");
          }
          vm.loading = false;
        }
      );
    },
    getGroupByCombo(comboId, search = "") {
      if (!comboId) return;
      const vm = this;
      const data = {
        comboId: comboId.id,
        departmentId: this.formData.department,
        search: search,
        isOnline: this.formData.isOnline ? 1 : 0,
        isVip: this.formData.isVip ? 1 : 0,
        byPassGroupLevel: this.byPassGroupLevel ? 1 : 0,
      };
      $.get(
        window.location.origin + "/backend/vip/get-group-by-courses",
        data,
        function (res) {
          if (res.code == 200) {
            if (vm.formData.isOnline) vm.groupListByCombo = res.data;
            else vm.offlineGroups = res.data;
          } else {
            alert("Có lỗi! Liên hệ dev!!!");
          }
          vm.loading = false;
        }
      );
    },
    kick(userId, groupId) {
      const vm = this;
      const data = {
        userId: userId,
        groupId: groupId,
        invoiceId: this.currentAddCourseData.invoice.id,
      };
      $.post(
        window.location.origin + "/backend/vip/kick-user",
        data,
        function (res) {
          if (res.code == 200) {
            vm.joinedGroupListByUser = vm.joinedGroupListByUser.filter(
              (g) => g.id !== groupId
            );
            vm.groupListByUser.unshift(res.data);
            vm.getInvoice(vm.currentAddCourseData.invoice.id);
          } else {
            alert("Có lỗi! Liên hệ bạn Quang 18 tủi");
          }
          vm.loading = false;
        }
      );
    },
    async addUser(invoiceId, userId, groupId, activeOnline) {
      const vm = this;
      if (vm.addUserLoading) return;
      vm.addUserLoading = true;
      const data = {
        invoiceId: invoiceId,
        userId: userId,
        groupId: groupId,
        activeOnline: activeOnline ? 1 : 0,
      };
      if (this.getGroupWatchExpiredStatus && activeOnline) {
        const check = confirm(
          "Học viên đang có khoá online còn hạn đến " +
            this.getGroupWatchExpiredStatus +
            ". Xác nhận kích hoạt?"
        );
        if (check) {
          await this.handleAddUser(data);
        }
      } else {
        await this.handleAddUser(data);
      }
      await this.getInvoice(invoiceId);
    },
    copyLinkChat(group_chat_id) {
      if (group_chat_id == null || group_chat_id == 'null') {
        alert('Nhóm chưa tạo group chat');
        return;
      }

      let currentUrl = (new URL(window.location.origin));

      let searchParams = currentUrl.searchParams;

      searchParams.append('chat', group_chat_id)

      currentUrl.search = searchParams.toString();

      navigator.clipboard.writeText(currentUrl.href).then(res => {
        alert('Đã copy thành công!');
      });
    },
    handleAddUser(data) {
      const vm = this;
      $.post(
        window.location.origin + "/backend/vip/add-user",
        data,
        function (res) {
          if (res.code == 200) {
            vm.groupListByUser = vm.groupListByUser.filter(
              (g) => g.id !== data.groupId
            );
            vm.joinedGroupListByUser.unshift(res.data);
            if (vm.filter.status === "new") {
              vm.results = vm.results.filter(
                (item) => item.id !== data.invoiceId
              );
            }
          }
          vm.addUserLoading = false;
        }
      ).fail(function (res) {
        if (res.status === 400) {
          alert(res.responseJSON.data.msg);
        } else {
          alert("Có lỗi! Liên hệ dev");
        }
      });
    },
    toggleAddUser(groupId) {
      // this.formData.activeOnline = false;
      if (!this.formData.groupId.includes(groupId)) {
        this.formData.groupId.push(groupId);
      } else {
        this.formData.groupId = this.formData.groupId.filter(
          (group) => group != groupId
        );
      }
    },
    getLevelStatus(invoice, courseName) {
      // console.log(
      //   _.findIndex(invoice.user?.offline_user?.courses, [
      //     "level_of_n",
      //     6 - courseName[1],
      //   ])
      // );
      if (!invoice.user) return -1;
      if (invoice.product_type === "offline")
        return _.findIndex(invoice.user?.offline_user?.courses, [
          "level_of_n",
          6 - courseName[1],
        ]);
      return _.findIndex(invoice.user.groups, ["vip_level", courseName]);
    },
    activeInvoice(id, type = "course") {
      const vm = this;
      vm.activeInvoiceLoading = true;
      var r = confirm("Bạn có chắc chắn muốn kích hoạt đơn hàng này ???");
      if (r == true) {
        $.ajax({
          type: "post",
          url: "/backend/invoice/do-active",
          data: {
            _token: $("input[name=_token]").val(),
            id: id,
            type: type,
          },
          success: function (data) {
            if (data == "success") {
              if (vm.filter.status === "new") {
                vm.results = vm.results.filter((item) => item.id !== id);
              }
              if (vm.filter.status === "completed") {
                vm.results = vm.results.map((item) => {
                  if (item.id === id) {
                    item.invoice_status = "completed";
                  }
                  return item;
                });
              }
            } else alert("Kích hoạt lỗi");
          },
        }).done(function () {
          vm.activeInvoiceLoading = false;
        });
      }
    },
    //tạo cuộc hội thoại với user chưa có
    initConversation: function (userId) {
      $.ajax({
        type: "post",
        url: "/backend/user/create-conversation",
        data: {
          id: userId,
        },
        success: function (response) {
          window.open(
            window.location.origin + "/backend/chat#" + response,
            "_blank"
          );
        },
      });
    },
    getColumn(path) {
      return _.get(this.columns, path);
    },
    getLogFrom(log) {
      if (log.column == 'invoice.department_id') {
        return this.departmentList.find(i => i.id == log.from)?.name ?? log.from
      }
      if (log.column == 'invoice.payment_method_id') {
        return this.paymentMethodList.find(i => i.id == log.from)?.name ?? log.from
      }
      return log.from ? log.from : 'null';
    },
    getLogTo(log) {
      if (log.column == 'invoice.department_id') {
        return this.departmentList.find(i => i.id == log.to)?.name ?? log.to
      }
      if (log.column == 'invoice.payment_method_id') {
        return this.paymentMethodList.find(i => i.id == log.to)?.name ?? log.to
      }
      return log.to ? log.to : 'null';
    },
    cancelInvoice(id) {
      const vm = this;
      var r = confirm("Bạn có chắc chắn muốn hủy đơn hàng này ?");
      if (r == true) {
        $.ajax({
          type: "post",
          url: "/backend/invoice/do-cancel",
          data: {
            _token: $("input[name=_token]").val(),
            id: id,
          },
          success: function (data) {
            if (data == "success") {
              if (vm.filter.status !== "cancel") {
                vm.results = vm.results.filter((item) => item.id !== id);
              }
            } else alert("Không hủy được");
          },
        });
      }
    },
    deleteInvoice(id) {
      const vm = this;
      var r = confirm("Bạn có chắc chắn muốn hủy đơn hàng này ?");
      if (r == true) {
        $.ajax({
          type: "delete",
          url: "/backend/invoice/" + id,
          data: {
            _token: $("input[name=_token]").val(),
          },
          success: function (data) {
            if (data == "success") {
              vm.results = vm.results.filter((item) => item.id !== id);
            } else alert("Không hủy được");
          },
        });
      }
    },
    activeOnline(userId, groupId, invoiceId) {
      const vm = this;
      vm.activeLoading = true;
      const data = {
        userId,
        groupId,
        invoiceId,
      };
      $.ajax({
        type: "post",
        url: "/backend/vip/active-online",
        data: {
          _token: $("input[name=_token]").val(),
          ...data,
        },
        success: function (data) {
          if (data == "success") {
            alert("Đã kích hoạt");
          }
          vm.activeLoading = false;
        },
      }).done(function () {
        vm.activeLoading = false;
      });
    },
    getGroupUserLength(group) {
      const idx = this.reservations.findIndex((r) => r.id === group.id);
      if (idx === -1) {
        return group.group_user.length;
      } else {
        return this.reservations[idx].count;
      }
    },
    getCurrencySymbol(currency) {
      return currency === "vnd" ? "đ" : "¥";
    },
    /* start export pdf offline invoice */
    invoiceDetail: function (invoice) {
      let vm = this;
      let disscount = parseInt(invoice.discount_money);
      let paid_money = parseInt(invoice.paid_money);
      let price = invoice.price - (disscount + paid_money);

      vm.printInvoice = {
        id: invoice.id,
        email: invoice.info_contact.email,
        fullname: invoice.info_contact.name,
        price: invoice.price,
        currency: invoice.currency,
        phone: invoice.info_contact.phone,
        product_type: invoice.product_type,
        product_name: invoice.product_name,
        discountMoney: invoice.discount_money,
        paidMoney: invoice.paid_money,
        createdAt: invoice.created_at,
        note: invoice.note,
        paidMoneyText: to_vietnamese(invoice.paid_money),
        oweMoney: price,
        gift: invoice.gift ? JSON.parse(invoice.gift) : vm.offlineData.gift,
        adminCreate: invoice.admin_active_name,
      };

      vm.showInvoiceDetail = true;
    },
    closeInvoiceDetail() {
      this.printInvoice = {};
      this.showInvoiceDetail = false;
    },
    closeInvoiceDept() {
      this.depositInvoice = {};
      this.showInvoiceDept = false;
    },
    downloadInvoice() {
      var vm = this;
      var elementHTML = document.getElementById("print-invoice");

      const { jsPDF } = window.jspdf;

      const pdf = new jsPDF("p", "mm", "a4");

      html2canvas(elementHTML, {
        x: 0,
        y: 5,
        witdth: 250,
        windowWidth: 1000,
        scale: 1.0,
      }).then(function (canvas) {
        const image = canvas.toDataURL();
        pdf.addImage(image, "PNG", 10, 10);
        pdf.autoPrint({ variant: "non-conform" });
        pdf.save("invoice" + vm.printInvoice.id + ".pdf");
      });
    },
    makeInvoicePaidMore(invoice) {
      this.cloneInvoice = true;
      let comboId = [];
      let vm = this;
      let price = parseInt(invoice.price);
      let disscount = parseInt(invoice.discount_money);
      let paid_money = parseInt(invoice.paid_money);
      let calPrice = price - (disscount + paid_money);

      if (["vip_combo", "offline"].includes(invoice.product_type)) {
        if (invoice.vip_combo) {
          comboId = invoice.vip_combo;
        }
      } else {
        if (invoice.combo) {
          if (!invoice.book || invoice.combo.id !== invoice.book.id) {
            comboId = invoice.combo;
          }
        }
      }
      this.formData = {
        email: invoice.info_contact.email,
        password: "",
        fullname: invoice.info_contact.name,
        phone: invoice.info_contact.phone,
        facebook: invoice.info_contact.facebook,
        chat: invoice.info_contact.chat,
        address: invoice.info_contact.address,
        
        comboId: comboId,
        coupon: invoice.coupon_id,
        price: calPrice,
        currency: invoice.currency,
        department: invoice.department?.id || null,
        paymentMethod: invoice.payment_method_id,
        paidAt: invoice.paid_at,
        note: invoice.note,
        discountMoney: 0,
        paidMoney: "",
        groupId: [],
        bookIds: [],
        refundBooks: [],
        paidFor: invoice.paid_for_id ? invoice.paid_for_id : invoice.id,
      };

      this.offlineData.gift = JSON.parse(invoice.gift);

      this.openCreate(["vip_combo", "vip_course"].includes(invoice.product_type) ||
          invoice.product_type === "offline", invoice.product_type !== "offline");
    },
    checkPaidMore(item) {
      let discount = parseInt(item.discount_money);
      let paid = parseInt(item.paid_money);
      let check = item.price - (discount + paid);

      if (item.price >= discount + paid && check !== 0) {
        return true;
      }
      return false;
    },
    /* end export pdf offline invoice */
    checkDidAdd(group) {
      const checkDidAddThisGroupType = this.joinedList.find(
        (g) => g.type === group.type && g.vip_level === group.vip_level
      );
      return !checkDidAddThisGroupType;
    },
    /* Tính tổng tiền đơn hàng */
    priceCalculate() {
      let total = 0;
      if (this.formData.bookIds.length) {
        this.formData.bookIds.forEach((item) => {
          let subPrice = item.quantity * item.price;
          total += subPrice;
        });
        this.bookData.totalPrice = total;
        if (this.formData.comboId && !Array.isArray(this.formData.comboId)) {
          total += parseInt(this.formData.comboId.price);
        }
        this.formData.price = total || undefined;
      } else {
        if (this.formData.comboId) {
          this.formData.price = this.formData.comboId.price || undefined;
        } else {
          this.formData.price = undefined;
        }
      }
    },
    changeBookStatus() {
      if (this.formData.refundBooks.length && this.formData.id)
        this.bookData.status = "return";
    },
    getBookList() {
      let vm = this;
      $.get(
        window.location.origin + "/backend/book-inventory/get-list",
        vm.bookData,
        function (res) {
          if (res.status == 200) {
            vm.bookList = res.data.map((item) => {
              return {
                ...item,
                inventory:
                  (parseInt(item.totalIn) || 0) -
                  (parseInt(item.totalOut) || 0),
                quantity: 1,
              };
            });
            vm.formData.bookIds.forEach((book, index) => {
              var invoiceObj = _.find(vm.bookList, { id: book.id });
              var cloneObj = { ...book };
              cloneObj.inventory = invoiceObj.inventory;
              if (invoiceObj) {
                vm.$set(vm.formData.bookIds, index, cloneObj);
              }
            });
          }
        }
      );
    },
    getCourses() {
      let vm = this;
      $.get(window.location.origin + "/backend/courses", function (res) {
        if (res.code == 200) {
          vm.courses = res.data;
        }
      });
    },
    getCourseFromService(courseId) {
      return this.validCourses.find(
        (course) => Number(course.id) === Number(courseId)
      );
    }
  },
  mounted: function () {
    const vm = this;
    vm.setFilterByUrl();
    // Loại bỏ các filter rỗng khỏi request
    var filter = _.omit(
      _.pickBy(vm.filter, function (value, key) {
        return value !== undefined && value !== null && value !== "";
      }),
      ["total_page"]
    );
    this.getList(filter);
    this.getBookList();
    //lắng nghe tin nhắn từ pusher
    Pusher.logToConsole = false;
    var pusher = new Pusher("7d39d4954f600d3bb86c", {
      cluster: "ap3",
    });
    var channel = pusher.subscribe("invoice");
    channel.bind("reservation", function (data) {
      const idx = vm.reservations.findIndex((r) => r.id === data.id);
      if (idx === -1) {
        vm.reservations.push(data);
      } else {
        vm.$set(vm.reservations, idx, data);
      }
    });
    this.getCourses();
  },
});
